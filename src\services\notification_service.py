import queue
import threading
import time
from datetime import datetime
from ..utils import logger

class NotificationService:
    """通知消息服务 - 管理外部推送的通知消息"""
    
    def __init__(self):
        # 为每个设备维护一个消息队列
        self.device_queues = {}
        self.lock = threading.Lock()
        
    def add_notification(self, android_id, notification_data):
        """添加通知消息到对应设备的队列"""
        try:
            with self.lock:
                if android_id not in self.device_queues:
                    self.device_queues[android_id] = queue.Queue()
                
                # 添加时间戳
                notification_data['timestamp'] = datetime.now().isoformat()
                notification_data['android_id'] = android_id
                
                self.device_queues[android_id].put(notification_data)
                logger.info(f"✅ 设备 {android_id} 添加通知: {notification_data.get('type', 'unknown')}")
                return True
                
        except Exception as e:
            logger.error(f"❌ 添加通知失败: {e}")
            return False
    
    def get_pending_notifications(self, android_id):
        """获取设备的待处理通知"""
        try:
            with self.lock:
                if android_id not in self.device_queues:
                    return []
                
                notifications = []
                device_queue = self.device_queues[android_id]
                
                # 获取所有待处理的通知
                while not device_queue.empty():
                    try:
                        notification = device_queue.get_nowait()
                        notifications.append(notification)
                    except queue.Empty:
                        break
                
                if notifications:
                    logger.info(f"📨 设备 {android_id} 获取到 {len(notifications)} 条待处理通知")
                
                return notifications
                
        except Exception as e:
            logger.error(f"❌ 获取通知失败: {e}")
            return []
    
    def has_pending_notifications(self, android_id):
        """检查设备是否有待处理通知"""
        try:
            with self.lock:
                if android_id not in self.device_queues:
                    return False
                return not self.device_queues[android_id].empty()
        except Exception as e:
            logger.error(f"❌ 检查通知失败: {e}")
            return False
    
    def clear_notifications(self, android_id):
        """清空设备的通知队列"""
        try:
            with self.lock:
                if android_id in self.device_queues:
                    while not self.device_queues[android_id].empty():
                        try:
                            self.device_queues[android_id].get_nowait()
                        except queue.Empty:
                            break
                    logger.info(f"🧹 设备 {android_id} 通知队列已清空")
        except Exception as e:
            logger.error(f"❌ 清空通知失败: {e}")
    
    def get_queue_size(self, android_id):
        """获取设备队列大小"""
        try:
            with self.lock:
                if android_id not in self.device_queues:
                    return 0
                return self.device_queues[android_id].qsize()
        except Exception as e:
            logger.error(f"❌ 获取队列大小失败: {e}")
            return 0

# 全局单例
notification_service = NotificationService()
