#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间滑动测试脚本
"""

import sys
import os
import time
import subprocess

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.xbot import DeviceSession

class SwipeTest:
    """专门测试时间滑动的类"""

    def __init__(self):
        self.device_session = None
        self.udid = self.get_connected_device()
        if not self.udid:
            raise RuntimeError("未找到可用设备")
        self.setup_device_session()

    def get_connected_device(self):
        """返回首个已连接设备的UDID"""
        try:
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
            lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
            for line in lines:
                if '\tdevice' in line:
                    udid = line.split('\t')[0]
                    print(f"✅ 找到设备: {udid}")
                    return udid
            print("❌ 未找到已连接的设备")
            return None
        except Exception as e:
            print(f"❌ 获取设备列表失败: {e}")
            return None

    def setup_device_session(self):
        """设置设备会话"""
        try:
            print(f"🔧 初始化设备会话: {self.udid}")
            self.device_session = DeviceSession(
                udid=self.udid,
                custom_name=f"滑动测试_{self.udid}",
                custom_udid=self.udid
            )
            self.device_session.connect()
            print("✅ 设备会话初始化成功")
        except Exception as e:
            print(f"❌ 设备会话初始化失败: {e}")
            raise
    
    def test_progressive_hour_swipe(self):
        """递进式小时滑动测试：基于00元素中心点滑动"""
        print("\n" + "="*50)
        print("🧪 递进式小时滑动测试 - 基于00元素中心")
        print("请确保手机已打开时间选择页面，当前显示00")
        print("="*50)

        # 等待用户准备
        input("按回车键开始测试...")

        # 先确定小时列的滑动基准点（优先用 00 的中心，找不到则用已知 ListView 中心）
        hour_x, hour_y = self.find_hour_00_center()
        if hour_x is None:
            # 小时ListView bounds="[624,489][800,2357]" → x≈712, y_center≈1423
            hour_x = (624 + 800) / 2
            hour_y = (489 + 2357) / 2
            print(f"📍 使用小时ListView中心作为滑动基准: ({hour_x:.0f}, {hour_y:.0f})")
        else:
            print(f"📍 使用小时00元素中心作为滑动基准: ({hour_x:.0f}, {hour_y:.0f})")

        # 测试序列：00 → 03 → 06 → 09 → 12
        targets = ["03", "06", "09", "12"]

        for i, target in enumerate(targets):
            print(f"\n🎯 第{i+1}步: 滑动到小时 {target}")

            # 基于同一列中心点进行稳定滑动（不再每次查找 00，避免滚出可视区后无法获取坐标）
            start_y = hour_y + 120
            end_y = hour_y - 120
            duration = 1200

            print(f"🔄 小时列滑动: x={hour_x:.0f}, {start_y:.0f} → {end_y:.0f} (duration={duration}ms)")
            try:
                self.device_session.swipe(hour_x, start_y, hour_x, end_y, duration=duration)
                time.sleep(1.2)  # 等待滑动完成

                # 查找目标数字
                print(f"🔍 查找目标数字: {target}")
                elements = self.device_session.find_all_by_xpath(f"//android.widget.TextView[@text='{target}']")

                found = False
                for el in elements:
                    bounds = el.get_bounding()
                    if bounds != [0, 0, 0, 0]:
                        x = (bounds[0] + bounds[2]) / 2
                        if 600 <= x < 850:  # 小时区域
                            print(f"✅ 找到小时{target}: ({x:.0f}, {(bounds[1] + bounds[3]) / 2:.0f})")
                            found = True
                            break

                if not found:
                    print(f"❌ 未找到小时{target}")

            except Exception as e:
                print(f"❌ 滑动失败: {e}")

            time.sleep(1.2)  # 等待观察

        print("\n✅ 递进式小时滑动测试完成！")

    def find_hour_00_center(self):
        """查找小时区域的00元素中心点"""
        try:
            elements = self.device_session.find_all_by_xpath("//android.widget.TextView[@text='00']")

            for el in elements:
                bounds = el.get_bounding()
                if bounds != [0, 0, 0, 0]:
                    x = (bounds[0] + bounds[2]) / 2
                    y = (bounds[1] + bounds[3]) / 2

                    # 检查是否在小时区域
                    if 600 <= x < 850:
                        print(f"📍 找到小时00元素中心: ({x:.0f}, {y:.0f})")
                        return x, y

            print("❌ 未找到小时区域的00元素")
            return None, None

        except Exception as e:
            print(f"❌ 查找00元素失败: {e}")
            return None, None

    def find_minute_00_center(self):
        """查找分钟区域的00元素中心点"""
        try:
            elements = self.device_session.find_all_by_xpath("//android.widget.TextView[@text='00']")

            for el in elements:
                bounds = el.get_bounding()
                if bounds != [0, 0, 0, 0]:
                    x = (bounds[0] + bounds[2]) / 2
                    y = (bounds[1] + bounds[3]) / 2

                    # 检查是否在分钟区域
                    if x >= 850:
                        print(f"📍 找到分钟00元素中心: ({x:.0f}, {y:.0f})")
                        return x, y

            print("❌ 未找到分钟区域的00元素")
            return None, None

        except Exception as e:
            print(f"❌ 查找分钟00元素失败: {e}")
            return None, None

    def method1_swipe_on_00_element(self):
        """方案1: 找到00元素，在其中心点滑动"""
        try:
            print("🔍 查找00元素...")
            elements = self.device_session.find_all_by_xpath("//android.widget.TextView[@text='00']")

            for el in elements:
                bounds = el.get_bounding()
                if bounds != [0, 0, 0, 0]:
                    x = (bounds[0] + bounds[2]) / 2
                    y = (bounds[1] + bounds[3]) / 2

                    # 检查是否在小时区域
                    if 600 <= x < 850:
                        # 在00元素上滑动
                        start_y = y + 50
                        end_y = y - 50
                        duration = 2000

                        print(f"📍 找到小时00元素: ({x:.0f}, {y:.0f})")
                        print(f"🔄 在00元素上滑动: {start_y:.0f} → {end_y:.0f}")
                        self.device_session.swipe(x, start_y, x, end_y, duration=duration)
                        return

            print("❌ 未找到小时区域的00元素")

        except Exception as e:
            print(f"❌ 方案1失败: {e}")
    
    def method2_fixed_coordinate_714(self):
        """方案2: 使用固定坐标714滑动"""
        try:
            x = 714  # 基于UI树分析的坐标
            start_y = 1400
            end_y = 1300
            duration = 2000

            print(f"🔄 固定坐标714滑动: x={x}, {start_y} → {end_y}")
            self.device_session.swipe(x, start_y, x, end_y, duration=duration)

        except Exception as e:
            print(f"❌ 方案2失败: {e}")
    
    def method3_listview_center(self):
        """方案3: 在ListView中心滑动"""
        try:
            # 小时ListView bounds="[624,489][800,2357]"
            x = (624 + 800) / 2  # ListView X中心 = 712
            y_center = (489 + 2357) / 2  # ListView Y中心 = 1423
            
            start_y = y_center + 100
            end_y = y_center - 100
            duration = 2000
            
            print(f"🔄 ListView中心滑动: x={x:.0f}, {start_y:.0f} → {end_y:.0f}")
            self.device_session.swipe(x, start_y, x, end_y, duration=duration)
            
        except Exception as e:
            print(f"❌ 方案3失败: {e}")
    
    def method4_large_swipe(self):
        """方案4: 大幅度滑动"""
        try:
            x = 714
            start_y = 1600
            end_y = 1200  # 大幅度400px
            duration = 1500  # 快一点
            
            print(f"🔄 大幅度滑动: x={x}, {start_y} → {end_y} (400px)")
            self.device_session.swipe(x, start_y, x, end_y, duration=duration)
            
        except Exception as e:
            print(f"❌ 方案4失败: {e}")
    
    def test_progressive_minute_swipe(self):
        """递进式分钟滑动测试：基于00元素中心点滑动"""
        print("\n" + "="*50)
        print("🧪 递进式分钟滑动测试 - 基于00元素中心")
        print("请确保手机已打开时间选择页面，当前显示00分钟")
        print("="*50)

        input("按回车键开始测试分钟滑动...")

        # 先确定分钟列的滑动基准点（优先用 00 的中心，找不到则用已知分钟列中心）
        minute_x, minute_y = self.find_minute_00_center()
        if minute_x is None:
            # 结合 UI：分钟列大致 x≥850，给一个安全的中心（例如 936, 1178 或根据你的设备调整）
            minute_x = 936
            minute_y = 1178
            print(f"📍 使用分钟列中心作为滑动基准: ({minute_x:.0f}, {minute_y:.0f})")
        else:
            print(f"📍 使用分钟00元素中心作为滑动基准: ({minute_x:.0f}, {minute_y:.0f})")

        # 测试序列：00 → 05 → 10 → 15 → 20
        targets = ["05", "10", "15", "20"]

        for i, target in enumerate(targets):
            print(f"\n🎯 第{i+1}步: 滑动到分钟 {target}")

            # 复用同一列中心进行稳定滑动
            start_y = minute_y + 120
            end_y = minute_y - 120
            duration = 1200

            print(f"🔄 分钟列滑动: x={minute_x:.0f}, {start_y:.0f} → {end_y:.0f} (duration={duration}ms)")
            try:
                self.device_session.swipe(minute_x, start_y, minute_x, end_y, duration=duration)
                time.sleep(1.2)  # 等待滑动完成

                # 查找目标数字
                print(f"🔍 查找目标数字: {target}")
                elements = self.device_session.find_all_by_xpath(f"//android.widget.TextView[@text='{target}']")

                found = False
                for el in elements:
                    bounds = el.get_bounding()
                    if bounds != [0, 0, 0, 0]:
                        x = (bounds[0] + bounds[2]) / 2
                        if x >= 850:  # 分钟区域
                            print(f"✅ 找到分钟{target}: ({x:.0f}, {(bounds[1] + bounds[3]) / 2:.0f})")
                            found = True
                            break

                if not found:
                    print(f"❌ 未找到分钟{target}")

            except Exception as e:
                print(f"❌ 滑动失败: {e}")

            time.sleep(1.2)  # 等待观察

        print("\n✅ 递进式分钟滑动测试完成！")

    def interactive_test(self):
        """交互式测试"""
        print("\n" + "="*50)
        print("🎮 交互式滑动测试")
        print("="*50)
        
        while True:
            print("\n请选择测试方案:")
            print("1 - 小时滑动方案1 (00元素中心)")
            print("2 - 小时滑动方案2 (固定坐标714)")
            print("3 - 小时滑动方案3 (ListView中心)")
            print("4 - 小时滑动方案4 (大幅度滑动)")
            print("5 - 分钟滑动方案1 (固定坐标936)")
            print("6 - 分钟滑动方案2 (ListView中心)")
            print("q - 退出")
            
            choice = input("\n输入选择: ").strip()
            
            if choice == '1':
                self.method1_swipe_on_00_element()
            elif choice == '2':
                self.method2_fixed_coordinate_714()
            elif choice == '3':
                self.method3_listview_center()
            elif choice == '4':
                self.method4_large_swipe()
            elif choice == '5':
                try:
                    x = 936
                    start_y = 1300
                    end_y = 1200
                    duration = 2000
                    print(f"🔄 分钟滑动: x={x}, {start_y} → {end_y}")
                    self.device_session.swipe(x, start_y, x, end_y, duration=duration)
                except Exception as e:
                    print(f"❌ 分钟滑动失败: {e}")
            elif choice == '6':
                try:
                    x = 936
                    y_center = 1178
                    start_y = y_center + 100
                    end_y = y_center - 100
                    duration = 2000
                    print(f"🔄 分钟ListView中心: x={x}, {start_y} → {end_y}")
                    self.device_session.swipe(x, start_y, x, end_y, duration=duration)
                except Exception as e:
                    print(f"❌ 分钟ListView滑动失败: {e}")
            elif choice.lower() == 'q':
                break
            else:
                print("❌ 无效选择")
            
            time.sleep(2)
    
    def cleanup(self):
        """清理资源"""
        if self.device_session:
            self.device_session.quit()
            print("✅ 设备会话已清理")

def main():
    """主函数"""
    tester = None
    try:
        tester = SwipeTest()
        
        print("\n请选择测试模式:")
        print("1 - 递进式小时滑动测试 (基于00元素中心)")
        print("2 - 递进式分钟滑动测试 (基于00元素中心)")
        print("3 - 交互式测试")

        choice = input("输入选择 (1-3): ").strip()

        if choice == '1':
            tester.test_progressive_hour_swipe()
        elif choice == '2':
            tester.test_progressive_minute_swipe()
        elif choice == '3':
            tester.interactive_test()
        else:
            print("❌ 无效选择")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    finally:
        if tester:
            tester.cleanup()

if __name__ == "__main__":
    main()
