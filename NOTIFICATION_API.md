# 小红书通知API文档

## 概述

新增了消息驱动的监听功能，可以通过API接收外部通知消息，触发小红书监听逻辑的执行。

## 功能特点

- ✅ 支持接收JSON格式的通知消息
- ✅ 只处理小红书应用（`com.xingin.xhs`）的通知
- ✅ 不影响现有的养号和发文流程
- ✅ 智能调度：优先处理通知，不干扰正常任务
- ✅ 支持多种通知类型：评论、私信、关注、点赞等
- ✅ 提供队列状态查询和管理功能

## API接口

### 1. 接收通知

**POST** `/api/xhs/notification`

发送通知消息到系统，触发监听逻辑。

**请求体示例：**
```json
{
    "android_id": "0b5c4c84bcf33a9a",
    "type": "comment",
    "app_package": "com.xingin.xhs",
    "title": "新评论",
    "text": "有人评论了你的笔记"
}
```

**参数说明：**
- `android_id` (必需): 设备的Android ID
- `type` (可选): 通知类型，支持 `comment`、`private`、`follow`、`like` 等
- `app_package` (必需): 应用包名，必须是 `com.xingin.xhs`
- `title` (可选): 通知标题
- `text` (可选): 通知内容

**响应示例：**
```json
{
    "error_code": 0,
    "message": "通知接收成功",
    "data": {
        "android_id": "0b5c4c84bcf33a9a",
        "type": "comment",
        "queue_size": 1
    }
}
```

### 2. 查询通知状态

**GET** `/api/xhs/notification/status?android_id=设备ID`

查询指定设备的通知队列状态。

**响应示例：**
```json
{
    "error_code": 0,
    "message": "获取状态成功",
    "data": {
        "android_id": "0b5c4c84bcf33a9a",
        "queue_size": 2,
        "has_pending": true
    }
}
```

### 3. 清空通知队列

**POST** `/api/xhs/notification/clear`

清空指定设备的通知队列。

**请求体示例：**
```json
{
    "android_id": "0b5c4c84bcf33a9a"
}
```

## 工作流程

### 1. 正常任务流程
```
启动 → 检查通知 → 发布任务 → 检查通知 → 养号任务 → 检查通知 → 等待
```

### 2. 通知处理优先级
1. **最高优先级**: 发布任务前的紧急通知
2. **高优先级**: 发布任务后的通知
3. **中优先级**: 养号任务前的通知
4. **低优先级**: 养号任务后的通知

### 3. 监听逻辑执行
当收到通知时，系统会执行完整的监听逻辑：
- 处理赞和收藏红点
- 处理关注通知
- 处理评论通知
- 处理私信消息
- 处理待回复消息

## 使用示例

### Python示例
```python
import requests

# 发送评论通知
notification = {
    "android_id": "0b5c4c84bcf33a9a",
    "type": "comment",
    "app_package": "com.xingin.xhs",
    "title": "新评论",
    "text": "有人评论了你的笔记"
}

response = requests.post(
    "http://localhost:8080/api/xhs/notification",
    json=notification
)
print(response.json())
```

### curl示例
```bash
# 发送通知
curl -X POST http://localhost:8080/api/xhs/notification \
  -H "Content-Type: application/json" \
  -d '{
    "android_id": "0b5c4c84bcf33a9a",
    "type": "comment",
    "app_package": "com.xingin.xhs",
    "title": "新评论",
    "text": "有人评论了你的笔记"
  }'

# 查询状态
curl "http://localhost:8080/api/xhs/notification/status?android_id=0b5c4c84bcf33a9a"

# 清空队列
curl -X POST http://localhost:8080/api/xhs/notification/clear \
  -H "Content-Type: application/json" \
  -d '{"android_id": "0b5c4c84bcf33a9a"}'
```

## 测试

运行测试脚本：
```bash
python test_notification.py
```

## 注意事项

1. **不影响现有流程**: 通知处理不会中断养号和发文任务
2. **智能调度**: 系统会在合适的时机处理通知
3. **队列管理**: 每个设备维护独立的通知队列
4. **错误处理**: 处理失败不会影响主要任务流程
5. **日志记录**: 所有通知处理都有详细的日志记录

## 日志示例

```
🔔 设备 0b5c4c84bcf33a9a 检测到待处理通知，优先处理通知
📨 设备 0b5c4c84bcf33a9a 获取到 1 条待处理通知
📱 处理通知: comment (时间: 2024-01-01T12:00:00)
🔔 [账号昵称] 开始执行通知触发的监听逻辑
✅ [账号昵称] 通知触发的监听逻辑执行完成
```
