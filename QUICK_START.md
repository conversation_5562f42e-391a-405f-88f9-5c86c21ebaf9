# 快速开始 - 通知驱动监听

## 功能说明

现在系统支持通过API接收外部通知，自动触发小红书监听逻辑，实现消息驱动的监听功能。

## 核心特性

✅ **不影响现有功能**: 养号和发文流程完全不受影响  
✅ **智能调度**: 在合适的时机处理通知，避免冲突  
✅ **实时响应**: 收到通知后立即处理，无需等待定时任务  
✅ **队列管理**: 支持多个通知排队处理  

## 使用方法

### 1. 启动服务
```bash
python main.py
```

### 2. 发送通知
当检测到小红书有新消息时，发送以下格式的POST请求：

```bash
curl -X POST http://localhost:8080/api/xhs/notification \
  -H "Content-Type: application/json" \
  -d '{
    "android_id": "0b5c4c84bcf33a9a",
    "type": "comment",
    "app_package": "com.xingin.xhs",
    "title": "",
    "text": ""
  }'
```

### 3. 系统自动处理
- 系统收到通知后会自动将其加入队列
- 在不影响养号和发文的前提下执行监听逻辑
- 处理完成后自动清理队列

## 支持的通知类型

- `comment`: 评论通知
- `private`: 私信通知  
- `follow`: 关注通知
- `like`: 点赞通知

## 处理时机

1. **发布任务前**: 优先处理紧急通知
2. **发布任务后**: 处理发布期间收到的通知
3. **养号任务前**: 处理养号前的通知
4. **养号任务后**: 处理养号期间收到的通知

## 监控和调试

### 查看队列状态
```bash
curl "http://localhost:8080/api/xhs/notification/status?android_id=0b5c4c84bcf33a9a"
```

### 清空队列
```bash
curl -X POST http://localhost:8080/api/xhs/notification/clear \
  -H "Content-Type: application/json" \
  -d '{"android_id": "0b5c4c84bcf33a9a"}'
```

### 运行测试
```bash
python test_notification.py
```

## 日志监控

系统会输出详细的日志信息：

```
🔔 设备 0b5c4c84bcf33a9a 检测到待处理通知，优先处理通知
📨 设备 0b5c4c84bcf33a9a 获取到 1 条待处理通知
📱 处理通知: comment (时间: 2024-01-01T12:00:00)
🔔 [账号昵称] 开始执行通知触发的监听逻辑
✅ [账号昵称] 通知触发的监听逻辑执行完成
```

## 注意事项

1. **只处理小红书**: `app_package` 必须是 `com.xingin.xhs`
2. **设备ID匹配**: `android_id` 需要与实际设备ID匹配
3. **不重复处理**: 同一时间段内多个通知只会触发一次完整监听
4. **错误恢复**: 处理失败不会影响主要任务流程

## 集成示例

如果你有外部监控系统，可以这样集成：

```python
import requests

def send_xhs_notification(device_id, notification_type):
    """发送小红书通知"""
    data = {
        "android_id": device_id,
        "type": notification_type,
        "app_package": "com.xingin.xhs",
        "title": "",
        "text": ""
    }
    
    try:
        response = requests.post(
            "http://localhost:8080/api/xhs/notification",
            json=data,
            timeout=5
        )
        return response.status_code == 200
    except Exception as e:
        print(f"发送通知失败: {e}")
        return False

# 使用示例
send_xhs_notification("0b5c4c84bcf33a9a", "comment")
```

现在你的系统就支持消息驱动的监听了！🎉
