import random
import threading
import time
from appium import webdriver
from appium.webdriver.webdriver import WebDriver
from appium.webdriver.webelement import WebElement
from appium.webdriver.common.appiumby import AppiumBy

from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.actions.action_builder import Action<PERSON>uilder
from selenium.webdriver.common.actions.pointer_actions import PointerInput

from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type
from selenium.common.exceptions import WebDriverException, NoSuchElementException

from .logger import logger
from appium import webdriver
from appium.options.android import UiAutomator2Options


def log_before_retry(retry_state):
    instance = retry_state.args[0]

    # 尝试获取一个有意义的名字用于日志
    context_name = ""
    if isinstance(instance, DeviceSession):
        context_name = instance.custom_name
    elif isinstance(instance, WebElement):
        # 如果是 WebElement 的方法失败了
        # 我们可以获取元素的 tag_name 或其他属性来标识它
        try:
            # 尝试获取元素的 tag_name 和 text，更有辨识度
            tag = instance.tag_name
            text = instance.text[:30]  # 只截取前30个字符避免日志过长
            context_name = f"元素(tag={tag}, text='{text}...')"
        except:
            # 如果获取属性失败，就用元素 ID
            context_name = f"元素(id={instance.id})"
    # 从 retry_state 中获取有用的信息
    attempt_number = retry_state.attempt_number
    exception = retry_state.outcome.exception()
    logger.warning(
        f"{context_name} 操作失败，准备进行第 {attempt_number} 次重试...原因: {type(exception).__name__} - {exception}"
    )


RETRYABLE_EXCEPTIONS = (WebDriverException,)
robust_retry = retry(
    stop=stop_after_attempt(2),
    wait=wait_fixed(2), # 每次重试前等待2秒
    retry=retry_if_exception_type(RETRYABLE_EXCEPTIONS),
    reraise=True, # 最终失败时重新抛出异常
    before_sleep=log_before_retry
)

class DeviceSession:
    _connect_lock = threading.Lock()

    def __init__(self, udid: str, custom_name: str = "", custom_udid: str = "", appium_server_url: str = "http://127.0.0.1:4723", system_port: str="8200"):
        self._driver: WebDriver | None = None
        self.udid = udid
        self.custom_name = custom_name
        self.custom_udid = custom_udid

        self.appium_server_url = appium_server_url
        self.capabilities = {
            "platformName": "Android",
            "appium:automationName": "UiAutomator2",
            "appium:udid": self.udid,
            "appium:deviceName": self.udid,
            "appium:systemPort": system_port,
            "appium:noReset": True,
            "appium:uiautomator2ServerLaunchTimeout": 120000,
            "appium:uiautomator2ServerInstallTimeout": 120000,
            'appium:newCommandTimeout': 3600,
            "appium:adbExecTimeout": 60000,  # ADB命令超时
            "appium:androidDeviceReadyTimeout": 120,  # 设备就绪超时
            "appium:clearSystemFiles": True,  # 清理系统文件
            "appium:androidInstallTimeout": 90000,
            "appium:appWaitDuration": 20000,
            "appium:ignoreHiddenApiPolicyError": True,  # 绕过Android隐藏API限制[3](@ref)
            "appium:uiautomator2ServerStartTimeout": 60000,  # 服务器启动超时[1](@ref)
            "appium:appLaunchTimeout": 60000,  # 应用启动超时
            "appium:settingsServerTimeout": 60000,  # Settings应用响应超时[9](@ref)
            "appium:enforceAppInstall": True,
            "appium:skipServerInstallation": False,  # 确保uiautomator2服务安装[2](@ref)

            "appium:settingsServerInstallTimeout": 60000,
            "appium:uiautomator2ServerReadTimeout": 60000,
            # 权限与后台管理
            "appium:autoGrantPermissions": True,
            "appium:disableWindowAnimation": True,
            "appium:dontStopAppOnReset": True,
            # 服务重试与存储
            "appium:uiautomator2ServerReinstallTimeout": 180000
        }
        self.appium_options = UiAutomator2Options().load_capabilities(self.capabilities)

    def _connect_with_retries(self, max_retries: int = 2, delay: int = 10) -> WebDriver:
        """
        [私有方法] 带重试逻辑的驱动连接器。
        成功则返回 driver 实例，失败则抛出异常。
        """
        last_exception = None
        for attempt in range(max_retries):
            try:
                logger.warning(f"设备 {self.udid} 开始连接 ({attempt + 1}/{max_retries})...")
                driver = webdriver.Remote(command_executor=self.appium_server_url, options=self.appium_options)
                logger.warning(f"{self.udid} 设备连接成功！")
                return driver
            except Exception as e:
                logger.error(f"设备 {self.udid} 连接失败: {e}")
                last_exception = e
                if attempt < max_retries - 1:
                    time.sleep(delay)

        # 所有重试失败后，抛出异常
        raise ConnectionError(f"设备 {self.udid} 达到最大重试次数，无法连接") from last_exception

    def connect(self):
        """
        第二阶段初始化：执行实际的连接操作。
        """
        if self._driver and self.is_alive():
            logger.warning(f"设备 {self.udid} 的 driver 已存在且活跃，无需重新连接。")
            return
        self._driver = self._connect_with_retries()
        # if self._connect_lock.acquire(timeout=120):
        #     try:
        #         if self._driver and self.is_alive():
        #             logger.warning(f"设备 {self.udid} 的 driver 已存在且活跃，无需重新连接。")
        #             return
        #         self._driver = self._connect_with_retries()
        #     finally:
        #         self._connect_lock.release()
        # else:
        #     logger.warning(f"设备 {self.udid} 获取全局连接锁超时")


    def is_alive(self) -> bool:
        """检查 driver 会话是否仍然活跃"""
        if not self._driver:
            return False
        try:
            self._driver.get_window_size()
            return True
        except WebDriverException:
            return False

    @robust_retry
    def activate_app(self,app_package_flag):
        logger.debug(f"{self.custom_name} ,pause 1s")
        random_pause(1)
        return self._driver.activate_app(app_package_flag)

    def quit(self):
        try:
            logger.info(f"{self.custom_name} 开始清理设备 {self.udid} 的资源...")

            if self._driver:
                logger.debug(f"{self.custom_name} 正在关闭WebDriver会话...")
                self._driver.quit()
                self._driver = None
                logger.debug(f"{self.custom_name} WebDriver会话已关闭")

        except Exception as e:
            logger.error(f"{self.custom_name} 退出设备会话时出错: {e}")

    def set_custom_name(self, custom_name=""):
        self.custom_name = custom_name

    def set_custom_udid(self, custom_udid=""):
        self.custom_udid = custom_udid

    def get_udid(self):
        return self._driver.capabilities.get('udid')

    @robust_retry
    def find_by_id(self, element_id) -> WebElement:
        logger.debug(f"{self.custom_name} 通过 ID 查找元素: '{element_id}',pause 1s")
        random_pause(0.5)
        return self._driver.find_element(by=AppiumBy.ID, value=element_id)

    @robust_retry
    def find_all_by_id(self, element_id) -> list[WebElement]:
        logger.debug(f"{self.custom_name} 通过 ID 查找所有元素: '{element_id}',pause 1s")
        random_pause(0.5)
        return self._driver.find_elements(by=AppiumBy.ID, value=element_id)

    @robust_retry
    def find_by_xpath(self, xpath) -> WebElement:
        logger.debug(f"{self.custom_name} 通过 XPath 查找元素: '{xpath}',pause 1s")
        random_pause(0.5)
        return self._driver.find_element(by=AppiumBy.XPATH, value=xpath)

    @robust_retry
    def find_all_by_xpath(self, xpath) -> list[WebElement]:
        logger.debug(f"{self.custom_name} 通过 XPath 查找所有元素: '{xpath}',pause 1s")
        random_pause(0.5)
        return self._driver.find_elements(by=AppiumBy.XPATH, value=xpath)
    
    @robust_retry
    def find_by_text(self, text) -> WebElement:
        # logger.debug(f"{self.custom_name} 通过文本查找元素: '{text}',pause 1s")
        random_pause(0.5)
        xpath = f"//*[@text='{text}']"
        return self._driver.find_element(by=AppiumBy.XPATH, value=xpath)

    @robust_retry
    def find_all_by_text(self, text) -> list[WebElement]:
        # logger.debug(f"{self.custom_name} 通过文本查找所有元素: '{text}',pause 1s")
        random_pause(0.5)
        xpath = f"//*[@text='{text}']"
        return self._driver.find_elements(by=AppiumBy.XPATH, value=xpath)
    
    @robust_retry
    def get_page_source(self) -> str:
        logger.debug(f"{self.custom_name} 获取页面源码...,pause 1s")
        random_pause(0.5)
        return self._driver.page_source

    @robust_retry
    def back(self):
        logger.debug(f"{self.custom_name} 执行返回操作...,pause 1s")
        random_pause(0.5)
        self._driver.back()

    @robust_retry
    def get_window_size(self) -> dict:
        logger.debug(f"{self.custom_name} get_window_size,pause 1s")
        random_pause(0.5)
        return self._driver.get_window_size()

    @robust_retry
    def swipe(self, start_x: float, start_y: float, end_x: float, end_y: float, duration: int = 300, stop_pause: float = 0.5):
        logger.debug(f"{self.custom_name} swipe,pause {stop_pause}s")
        random_pause(stop_pause)
        """
        [ActionChains 实现] 从 (start_x, start_y) 滑动到 (end_x, end_y)。
        """
        int_sx, int_sy, int_ex, int_ey = map(int, [start_x, start_y, end_x, end_y])
        logger.debug(f"'{self.custom_name}' 从 ({int_sx}, {int_sy}) 滑动到 ({int_ex}, {int_ey})，耗时 {duration}ms")

        actions = ActionChains(self._driver)

        pointer = PointerInput("touch", "finger")
        actions.w3c_actions = ActionBuilder(self._driver, mouse=pointer)

        # 构建滑动动作链
        (
            actions.w3c_actions.pointer_action
            .move_to_location(int_sx, int_sy)
            .pointer_down()
            .pause(duration / 1000)  # pause 接受秒为单位
            .move_to_location(int_ex, int_ey)
            .release()
        )
        actions.perform()
        random_pause(stop_pause)

    @robust_retry
    def close_app(self, package_name, stop_pause: float = 0.5):
        logger.debug(f"{self.custom_name} close_app,pause {stop_pause}s")
        random_pause(stop_pause)

        """关闭指定的手机应用"""
        try:
            self._driver.terminate_app(package_name)
            logger.info(f"{self.custom_name} 成功关闭应用: {package_name}")
        except Exception as e:
            logger.error(f"{self.custom_name} 关闭应用失败: {package_name}, 错误: {e}")
            raise

    @robust_retry
    def get_clipboard(self) -> str:
        """
        获取设备剪贴板内容
        """
        logger.debug(f"{self.custom_name} get_clipboard,pause 1s")
        random_pause(0.5)
        logger.debug(f"{self.custom_name} 获取剪贴板内容...")
        try:
            clipboard_content = self._driver.get_clipboard_text()
            logger.debug(f"{self.custom_name} 剪贴板内容: {clipboard_content[:100]}...")
            return clipboard_content
        except Exception as e:
            logger.error(f"{self.custom_name} 获取剪贴板失败: {e}")
            return ""

    @robust_retry
    def set_clipboard(self, text: str):
        """
        设置设备剪贴板内容
        """
        logger.debug(f"{self.custom_name} set_clipboard,pause 1s")
        random_pause(0.5)
        logger.debug(f"{self.custom_name} 设置剪贴板内容: {text[:100]}...")
        try:
            self._driver.set_clipboard_text(text)
            logger.debug(f"{self.custom_name} 剪贴板设置成功")
        except Exception as e:
            logger.error(f"{self.custom_name} 设置剪贴板失败: {e}")

    @robust_retry
    def tap(self, x: int, y: int, duration: int = 100):
        """点击屏幕上的指定坐标"""
        logger.debug(f"{self.custom_name} 在坐标 ({x}, {y}) 点击屏幕, 持续 {duration}ms")

        actions = ActionChains(self._driver)
        pointer = PointerInput("touch", "finger")  # Or "mouse", depending on device
        actions.w3c_actions = ActionBuilder(self._driver, mouse=pointer)

        (
            actions.w3c_actions.pointer_action
            .move_to_location(x, y)
            .pointer_down()
            .pause(duration / 1000)  # Convert ms to seconds
            .pointer_up()
        )

        actions.perform()
        random_pause(0.3)

# 使用"猴子补丁"，将方法添加到 Appium 的 WebElement 类上
@robust_retry
def get_bounding(self: WebElement):
    logger.debug("get_bounding,pause 1s")
    random_pause(0.5)
    loc = self.location
    size = self.size
    x1 = loc['x']
    y1 = loc['y']
    x2 = x1 + size['width']
    y2 = y1 + size['height']
    return [x1, y1, x2, y2]
WebElement.get_bounding = get_bounding

@robust_retry
def input(self: WebElement, text_to_send):
    logger.debug("input,pause 1s")
    random_pause(0.5)
    self.clear()
    self.send_keys(text_to_send)
WebElement.input = input

@robust_retry
def dbclick(self: WebElement):
    logger.debug("dbclick,pause 0.5s")
    random_pause(0.5)

    driver = self.parent
    actions = ActionChains(driver)
    pointer = PointerInput("touch", "finger")  # Or "mouse", depending on device
    actions.w3c_actions = ActionBuilder(driver, mouse=pointer)

    # 1. Move to the element
    x = self.location['x'] + self.size['width'] / 2
    y = self.location['y'] + self.size['height'] / 2

    # 2. First Click
    actions.w3c_actions.pointer_action.move_to_location(int(x), int(y))
    actions.w3c_actions.pointer_action.pointer_down()
    actions.w3c_actions.pointer_action.pause(0.01)  # Short pause
    actions.w3c_actions.pointer_action.pointer_up()
    actions.w3c_actions.pointer_action.pause(0.01)  # Another short pause

    # 3. Second Click
    actions.w3c_actions.pointer_action.pointer_down()
    actions.w3c_actions.pointer_action.pointer_up()
    actions.perform()
    random_pause(0.5)
WebElement.dbclick = dbclick


@robust_retry
def longpress(self: WebElement, anchor_type: str = 'center', duration: int = 1500):
    """
    对 WebElement 执行长按操作。
    已更新为 selenium 4.x+ 的标准 W3C Actions API 用法。

    :param self: WebElement 实例 (自动传入)
    :param anchor_type: 长按在元素上的锚点位置。
                        可选值: 'center', 'top_left', 'top_right',
                                'bottom_left', 'bottom_right'。
                        默认为 'center'。
    :param duration: 长按的持续时间（毫秒）。默认为 1500ms。
    """
    # self.parent 就是这个元素所属的 WebDriver 实例
    logger.debug("longpress,pause 1s")

    random_pause(0.5)
    driver = self.parent

    # 1. 获取元素的边界框和中心点
    try:
        bounds = self.get_bounding()  # [x1, y1, x2, y2]
        x1, y1, x2, y2 = bounds
        center_x = (x1 + x2) / 2
        center_y = (y1 + y2) / 2
    except Exception as e:
        logger.error(f"长按前获取元素边界失败: {e}")
        raise  # 重新抛出异常，让 tenacity 处理重试

    # 2. 根据 anchor_type 计算目标坐标
    target_x, target_y = 0, 0
    if anchor_type == 'center':
        target_x, target_y = center_x, center_y
    elif anchor_type == 'top_left':
        target_x, target_y = x1, y1
    elif anchor_type == 'top_right':
        target_x, target_y = x2, y1
    elif anchor_type == 'bottom_left':
        target_x, target_y = x1, y2
    elif anchor_type == 'bottom_right':
        target_x, target_y = x2, y2
    else:
        logger.warning(f"无效的 anchor_type '{anchor_type}'，将默认使用 'center'。")
        target_x, target_y = center_x, center_y

    int_x, int_y = int(target_x), int(target_y)

    logger.debug(
        f"准备在坐标 ({int_x}, {int_y}) 对元素 (id={self.id}) "
        f"执行长按，持续 {duration}ms"
    )

    # a. 创建 ActionChains 实例作为操作入口
    actions = ActionChains(driver)

    # b. 定义指针输入源 ("touch" 类型)，并将其与 ActionBuilder 关联
    pointer = PointerInput("touch", "finger")
    actions.w3c_actions = ActionBuilder(driver, mouse=pointer)

    (
        actions.w3c_actions.pointer_action
        .move_to_location(int_x, int_y)  # 动作1: 移动到目标坐标
        .pointer_down()  # 动作2: 手指按下
        .pause(duration / 1000)  # 动作3: 按住并暂停（实现长按）
        .pointer_up()  # 动作4: 手指抬起
    )

    # d. 执行整个动作序列
    actions.perform()

    random_pause(0.3)  # 操作后短暂等待，让 UI 有时间响应

WebElement.longpress = longpress

def find_element_by_id(self, element_id):
    logger.debug("find_element_by_id,pause 1s")

    random_pause(0.5)
    return self.find_element(AppiumBy.ID, element_id)
WebElement.find_element_by_id = find_element_by_id

def find_elements_by_id(self, element_id):
    logger.debug("find_elements_by_id,pause 1s")
    random_pause(0.5)
    return self.find_elements(AppiumBy.ID, element_id)
WebElement.find_elements_by_id = find_elements_by_id

@robust_retry
def click(self: WebElement):
    logger.debug("click,pause 0.5s")
    random_pause(0.5)
    WebElement._original_click(self)
    random_pause(0.5)

WebElement._original_click = WebElement.click
WebElement.click = click


def random_pause(base_delay):
    if base_delay==0:
        return
    fluctuation = 0.1  # 浮动比例 10%

    # 计算浮动范围
    lower_bound = base_delay * (1 - fluctuation)
    upper_bound = base_delay * (1 + fluctuation)

    # 生成随机延迟时间
    delay = random.uniform(lower_bound, upper_bound)

    # 暂停
    time.sleep(delay)