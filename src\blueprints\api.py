
from flask import Blueprint, jsonify, request
from flask import current_app
from ..utils import logger
from ..services.adb_service import pair
from ..services.notification_service import notification_service


api_blueprint = Blueprint('api', __name__)

@api_blueprint.route('/adb/pair', methods=['GET'])
def get_device_data():
    ips = request.args.get('ips')
    port = request.args.get('port', type=int)
    code = request.args.get('code')

    if not ips or not port or not code:
        return jsonify({
            "error_code": 400,
            "message": "Missing required parameters: ip, port, or code.",
            "data": {}
        }),400

    try:
        for ip in ips.split(','):
            ip = ip.strip()
            if ip:
                result = pair(ip, port, code)
                if result:
                    return jsonify({
                        "error_code": 0,
                        "message": "",
                        "data": result
                    }), 200
    except Exception as e:
        1

    return jsonify({
        "error_code": 500,
        "message": f"Pairing failed",
        "data": {}
    }), 500


@api_blueprint.route('/xhs/notes-stats/status', methods=['GET'])
def get_notes_stats_status():
    """获取笔记统计状态"""
    try:
        from datetime import datetime

        current_time = datetime.now()
        current_hour = current_time.hour
        current_minute = current_time.minute

        # 检查是否在统计时间窗口内
        is_stats_time = (current_hour == 23 and current_minute >= 30) or \
                       (current_hour == 0 and current_minute <= 30)

        return jsonify({
            "error_code": 0,
            "message": "获取状态成功",
            "data": {
                "current_time": current_time.strftime("%H:%M:%S"),
                "is_stats_time": is_stats_time,
                "stats_window": "每天 23:30-00:30",
                "mode": "集成模式（统计功能已集成到主任务循环）"
            }
        }), 200

    except Exception as e:
        logger.error(f"获取笔记统计状态失败: {e}")
        return jsonify({
            "error_code": 500,
            "message": f"获取状态失败: {str(e)}",
            "data": {}
        }), 500


@api_blueprint.route('/notification/xiaohongshu', methods=['POST'])
def receive_notification():
    """接收小红书通知消息"""
    try:
        data = request.get_json()

        # 验证必要字段
        if not data:
            return jsonify({
                "error_code": 400,
                "message": "请求体不能为空",
                "data": {}
            }), 400

        android_id = data.get('android_id')
        app_package = data.get('app_package')
        notification_type = data.get('type')

        if not android_id:
            return jsonify({
                "error_code": 400,
                "message": "缺少必要参数: android_id",
                "data": {}
            }), 400

        if not app_package:
            return jsonify({
                "error_code": 400,
                "message": "缺少必要参数: app_package",
                "data": {}
            }), 400

        # 只处理小红书的通知
        if app_package != "com.xingin.xhs":
            return jsonify({
                "error_code": 200,
                "message": f"忽略非小红书应用通知: {app_package}",
                "data": {}
            }), 200

        # 添加通知到队列
        success = notification_service.add_notification(android_id, data)

        if success:
            queue_size = notification_service.get_queue_size(android_id)
            logger.info(f"📨 收到设备 {android_id} 的 {notification_type} 通知，当前队列大小: {queue_size}")

            return jsonify({
                "error_code": 0,
                "message": "通知接收成功",
                "data": {
                    "android_id": android_id,
                    "type": notification_type,
                    "queue_size": queue_size
                }
            }), 200
        else:
            return jsonify({
                "error_code": 500,
                "message": "通知处理失败",
                "data": {}
            }), 500

    except Exception as e:
        logger.error(f"接收通知失败: {e}")
        return jsonify({
            "error_code": 500,
            "message": f"服务器错误: {str(e)}",
            "data": {}
        }), 500


@api_blueprint.route('/notification/xiaohongshu/status', methods=['GET'])
def get_notification_status():
    """获取通知队列状态"""
    try:
        android_id = request.args.get('android_id')

        if not android_id:
            return jsonify({
                "error_code": 400,
                "message": "缺少必要参数: android_id",
                "data": {}
            }), 400

        queue_size = notification_service.get_queue_size(android_id)
        has_pending = notification_service.has_pending_notifications(android_id)

        return jsonify({
            "error_code": 0,
            "message": "获取状态成功",
            "data": {
                "android_id": android_id,
                "queue_size": queue_size,
                "has_pending": has_pending
            }
        }), 200

    except Exception as e:
        logger.error(f"获取通知状态失败: {e}")
        return jsonify({
            "error_code": 500,
            "message": f"服务器错误: {str(e)}",
            "data": {}
        }), 500


@api_blueprint.route('/notification/xiaohongshu/clear', methods=['POST'])
def clear_notifications():
    """清空指定设备的通知队列"""
    try:
        data = request.get_json()
        android_id = data.get('android_id') if data else None

        if not android_id:
            return jsonify({
                "error_code": 400,
                "message": "缺少必要参数: android_id",
                "data": {}
            }), 400

        notification_service.clear_notifications(android_id)

        return jsonify({
            "error_code": 0,
            "message": "通知队列已清空",
            "data": {
                "android_id": android_id
            }
        }), 200

    except Exception as e:
        logger.error(f"清空通知队列失败: {e}")
        return jsonify({
            "error_code": 500,
            "message": f"服务器错误: {str(e)}",
            "data": {}
        }), 500
