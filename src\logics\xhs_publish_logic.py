import time
import random
import os
import subprocess
import json
import requests
import re
from datetime import datetime
from urllib.parse import urlparse

from ..utils import logger, glv, CommonMobileUtils

# 手机型号配置（内联定义）
PHONE_MODEL_CONFIG = {
    "HUAWEI": {"album_package": "com.android.gallery3d", "confirm_button": "确定"},
    "MI": {"album_package": "com.miui.gallery", "confirm_button": "确定"},
    "Redmi": {"album_package": "com.miui.gallery", "confirm_button": "确定"},
    "OPPO": {"album_package": "com.coloros.gallery3d", "confirm_button": "确定"},
    "vivo": {"album_package": "com.android.gallery3d", "confirm_button": "确定"},
    "SM-": {"album_package": "com.sec.android.gallery3d", "confirm_button": "确定"},
    "OnePlus": {"album_package": "com.oneplus.gallery", "confirm_button": "确定"},
    "meizu": {"album_package": "com.meizu.media.gallery", "confirm_button": "确定"},
    "DEFAULT": {"album_package": "com.android.gallery3d", "confirm_button": "确定"}
}

def get_phone_config(phone_model):
    """根据手机型号获取配置"""
    if not phone_model:
        return PHONE_MODEL_CONFIG["DEFAULT"]
    for model_key, config in PHONE_MODEL_CONFIG.items():
        if model_key in phone_model.upper():
            return config
    return PHONE_MODEL_CONFIG["DEFAULT"]


class XhsPublishLogic:
    """小红书笔记发布逻辑类"""
    
    def __init__(self, custom_udid, phone_model, domain=None):
        self.domain = domain
        self.custom_udid = custom_udid
        self.phone_model = phone_model
        self.device_session = None
        self.phone_config = get_phone_config(phone_model)
        logger.info(f"设备 {custom_udid} 型号: {phone_model}, 使用配置: {self.phone_config}")
        
    def init(self, device_session):
        """初始化设备会话"""
        self.device_session = device_session
        
    def ensure_app_ready(self):
        """确保应用已启动并处于正常状态"""
        try:
            # 检查页面是否在小红书
            page_source = self.device_session.get_page_source()
            if "com.xingin.xhs" not in page_source:
                self.device_session.activate_app("com.xingin.xhs")
                wait_time = random.randint(10, 15)
                logger.info(f"等待 {wait_time} 秒让 App 启动...")
                time.sleep(wait_time)

            # 检查页面是否有稍后再说按钮
            page_source = self.device_session.get_page_source()
            if "com.xingin.xhs:id/ali" in page_source:
                self.device_session.find_by_id("com.xingin.xhs:id/ali").click()
                time.sleep(2)

            # 确保回到首页
            self.ensure_homepage()

        except Exception as e:
            logger.error(f"应用初始化失败: {e}")

    def ensure_homepage(self):
        """确保当前在首页"""
        try:
            # 首先检查当前是否已经在首页
            page_source = self.device_session.get_page_source()
            if "com.xingin.xhs:id/du5" in page_source:
                logger.info("当前已在首页，无需返回")
                return

            # 最多返回8次，每次都检查是否有首页元素
            for i in range(8):
                # 返回一次
                logger.info(f"第{i+1}次返回操作")
                self.device_session.back()
                time.sleep(2)  # 等待页面加载

                # 检查是否有首页元素
                page_source = self.device_session.get_page_source()
                if "com.xingin.xhs:id/du5" in page_source:
                    logger.info(f"第{i+1}次检查：已找到首页元素")
                    return  # 找到首页元素就退出

            # 8次都没找到首页元素
            logger.warning("返回8次后仍未找到首页元素")

        except Exception as e:
            logger.error(f"回到首页失败: {e}")

    def clean_device_media(self):
        """清理设备相册中的所有图片和视频"""
        try:
            logger.info("开始清理设备相册...")

            # 清理常见的相册目录
            media_dirs = [
                "/sdcard/DCIM/Camera/",
                "/sdcard/Pictures/",
                "/sdcard/Download/",
                "/sdcard/Movies/",
                "/sdcard/"
            ]

            for media_dir in media_dirs:
                try:
                    # 删除图片文件
                    subprocess.run([
                        'adb', '-s', self.custom_udid, 'shell',
                        f'find {media_dir} -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -o -name "*.gif" -o -name "*.bmp" | xargs rm -f'
                    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=30)

                    # 删除视频文件
                    subprocess.run([
                        'adb', '-s', self.custom_udid, 'shell',
                        f'find {media_dir} -name "*.mp4" -o -name "*.avi" -o -name "*.mov" -o -name "*.mkv" -o -name "*.flv" -o -name "*.wmv" | xargs rm -f'
                    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=30)

                except Exception as e:
                    logger.warning(f"清理目录 {media_dir} 失败: {e}")

            # 刷新媒体库
            try:
                subprocess.run([
                    'adb', '-s', self.custom_udid, 'shell',
                    'am broadcast -a android.intent.action.MEDIA_MOUNTED -d file:///sdcard'
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=10)
            except Exception as e:
                logger.warning(f"刷新媒体库失败: {e}")

            logger.info("设备相册清理完成")

        except Exception as e:
            logger.error(f"清理设备相册失败: {e}")

    def check_fabu_config(self, account_number):
        """检查是否有符合条件的发布内容"""
        try:
            logger.info(f"检查账号 {account_number} 的待发布内容...")
            time.sleep(random.randint(5, 10))

            url = "https://api.mingdao.com/v2/open/worksheet/getFilterRows"
            headers = {"Content-Type": "application/json"}
            data = {
                "appKey": "f08bf7f7cfe8c038",
                "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
                "worksheetId": "jzzhnrff",
                "pageSize": 1000,
                "pageIndex": 1,
                "controls": [],
                "filters": [
                    {"controlId": "release_status", "dataType": 2, "spliceType": 1, "filterType": 1, "value": "未发布"},
                    {"controlId": "channel_type", "dataType": 2, "spliceType": 1, "filterType": 1, "value": "小红书"}
                ]
            }

            response = requests.post(url, headers=headers, json=data)
            if response.status_code == 200:
                res = response.json()
                if res.get("success") and "rows" in res.get("data", {}):
                    rows = res["data"]["rows"]
                    for row in rows:
                        account_data_str = row.get("account", "")
                        release_time = row.get("release_time")

                        if account_data_str:
                            try:
                                account_data = json.loads(account_data_str)
                            except json.JSONDecodeError as e:
                                logger.error(f"account JSON 解析失败: {e}, 原始数据: {account_data_str}")
                                continue

                            if isinstance(account_data, list):
                                for account_item in account_data:
                                    try:
                                        source_value = json.loads(account_item.get("sourcevalue", "{}"))
                                    except json.JSONDecodeError as e:
                                        logger.error(f"sourcevalue JSON 解析失败: {e}, 原始数据: {account_item.get('sourcevalue', '{}')}")
                                        continue
                                    account_in_data = source_value.get("66d7fffe98435d4ec600ca08", "")
                                    if account_in_data.strip().lower() == account_number.strip().lower():
                                        release_type = row.get("release_type", "")

                                        # 如果是定时发布，直接返回记录
                                        if release_type == "定时发布":
                                            logger.info(f"找到定时发布内容: {row.get('title', '无标题')}")
                                            return row

                                        # 如果是立即发布，检查时间是否到达
                                        if release_time:
                                            release_time_obj = datetime.strptime(release_time, "%Y-%m-%d %H:%M:%S")
                                            if datetime.now() >= release_time_obj:
                                                logger.info(f"找到待发布内容: {row.get('title', '无标题')}")
                                                return row  # 直接返回符合条件的发布记录

            logger.info("没有找到符合条件的待发布内容")
            return None

        except Exception as e:
            logger.error(f"检查发布配置失败: {e}")
            return None

    def clean_filename(self, filename):
        """清理文件名中的非法字符"""
        return re.sub(r'[\\/*?:"<>|]', "_", filename)

    def get_filename_from_url(self, url):
        """从URL中提取文件名"""
        parsed_url = urlparse(url)
        file_name = os.path.basename(parsed_url.path)
        return file_name.split('?')[0]

    def get_media_type(self, file_name):
        """根据文件扩展名判断文件类型"""
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv']

        _, ext = os.path.splitext(file_name.lower())

        if ext in image_extensions:
            return 'image'
        elif ext in video_extensions:
            return 'video'
        else:
            return 'unknown'

    def download_media(self, item):
        """下载媒体文件并推送到设备（包括封面）"""
        media_types = []

        try:
            # 处理 tp_sp
            tp_sp = item.get("tp_sp", [])
            if isinstance(tp_sp, str):
                # 检查字符串是否为空或只包含空白字符
                tp_sp = tp_sp.strip()
                if tp_sp:
                    try:
                        tp_sp = json.loads(tp_sp)
                    except json.JSONDecodeError as e:
                        logger.error(f"tp_sp JSON 解析失败: {e}, 原始数据: {tp_sp}")
                        tp_sp = []
                else:
                    logger.warning("tp_sp 为空字符串，跳过")
                    tp_sp = []

            if isinstance(tp_sp, list):
                for media_info in tp_sp:
                    download_url = media_info.get("DownloadUrl")
                    if not download_url:
                        logger.warning("未找到有效的文件下载URL")
                        continue

                    logger.info(f"下载文件: {download_url}")
                    self._download_and_push_to_device(download_url, media_types)

            else:
                logger.warning(f"tp_sp 格式不正确，类型: {type(tp_sp)}, 值: {tp_sp}")

            # ✅ 处理 video_cover，逻辑与 tp_sp 完全一致
            video_cover = item.get("video_cover", [])
            if isinstance(video_cover, str):
                # 检查字符串是否为空或只包含空白字符
                video_cover = video_cover.strip()
                if video_cover:
                    try:
                        video_cover = json.loads(video_cover)
                    except json.JSONDecodeError as e:
                        logger.error(f"video_cover JSON 解析失败: {e}, 原始数据: {video_cover}")
                        video_cover = []
                else:
                    logger.warning("video_cover 为空字符串，跳过")
                    video_cover = []

            if isinstance(video_cover, list):
                for cover_info in video_cover:
                    download_url = cover_info.get("DownloadUrl")
                    if not download_url:
                        logger.warning("未找到封面下载URL")
                        continue

                    logger.info(f"下载封面文件（video_cover）: {download_url}")
                    self._download_and_push_to_device(download_url, media_types, is_cover=True)
            else:
                logger.info(f"video_cover 格式不正确，类型: {type(video_cover)}, 跳过封面下载")

            # 返回文件类型
            if 'video' in media_types:
                return 'video'
            elif 'image' in media_types:
                return 'image'
            else:
                return 'unknown'

        except Exception as e:
            logger.error(f"下载媒体文件失败: {e}")
            return None

    def _download_and_push_to_device(self, download_url, media_types, is_cover=False):
        try:
            response = requests.get(download_url, stream=True, timeout=30)
            if response.status_code != 200:
                logger.error(f"文件下载失败，状态码: {response.status_code}")
                return

            # 获取清理后的文件名
            file_name = self.get_filename_from_url(download_url)
            file_name = self.clean_filename(file_name)

            # 如果是封面，可以在文件名前打个标记
            if is_cover:
                file_name = f"cover_{file_name}"

            remote_file_path = f"/sdcard/{file_name}"

            import tempfile
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                for chunk in response.iter_content(chunk_size=1024):
                    if chunk:
                        temp_file.write(chunk)
                temp_file_path = temp_file.name

            result = subprocess.run([
                'adb', '-s', self.custom_udid, 'push', temp_file_path, remote_file_path
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=60)

            os.unlink(temp_file_path)

            if result.returncode == 0:
                logger.info(f"文件已推送到设备: {remote_file_path}")

                # 刷新媒体库
                subprocess.run([
                    'adb', '-s', self.custom_udid, 'shell',
                    f'am broadcast -a android.intent.action.MEDIA_SCANNER_SCAN_FILE -d file://{remote_file_path}'
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=10)

                media_type = self.get_media_type(file_name)
                media_types.append(media_type)
            else:
                logger.error(f"推送文件失败: {result.stderr}")

        except Exception as e:
            logger.error(f"处理下载URL {download_url} 时发生错误: {e}")


    def click_publish_button(self):
        """点击发布按钮"""
        try:
            publish_button = self.device_session.find_by_id("com.xingin.xhs:id/du8")
            publish_button.click()
            logger.info("发布按钮已点击")
            return True
        except Exception as e:
            logger.error(f"点击发布按钮失败: {e}")
            return False

    def click_fabu_follow(self, media_type):
        """点击发布后续流程"""
        try:
            # 获取所有符合条件的元素
            elements = self.device_session.find_all_by_id("com.xingin.xhs:id/dr9")

            if media_type == "video":
                video_button = self.device_session.find_all_by_id("com.xingin.xhs:id/ig4")[0]
                video_button.click()
                shipin_elements = self.device_session.find_all_by_id("com.xingin.xhs:id/dr9")
                shipin_elements[0].click()
            else:
                # 循环点击每一个元素
                for element in elements:
                    element.click()

            # 点击第一个图片/视频
            first_media = self.device_session.find_all_by_id("com.xingin.xhs:id/ffv")[0]
            first_media.click()

            # 点击下一步
            next_button = self.device_session.find_by_id("com.xingin.xhs:id/a_v")
            next_button.click()

            time.sleep(random.randint(10, 30))

            # 点击视频或者图片下一步
            next_button2 = self.device_session.find_by_id("com.xingin.xhs:id/aqe")
            next_button2.click()

            return True

        except Exception as e:
            logger.error(f"点击发布后续流程失败: {e}")
            return False

    def extract_and_publish(self, data):
        """提取数据并填写发布内容"""
        try:
            # 提取标题和正文
            title = data.get("title", "默认标题")
            zhengwen = data.get("zhengwen", "默认正文")
            topics = data.get("topic_word", "")
            topic_list = topics.split(",") if topics else []

            # 输入标题
            title_input = self.device_session.find_by_id("com.xingin.xhs:id/c3j")
            title_input.input(title)

            # 输入正文
            logger.info(f"输入正文: {zhengwen}")
            zhengwen_input = self.device_session.find_by_id("com.xingin.xhs:id/gqy")
            # zhengwen_input.click()
            zhengwen_input.input(zhengwen)

            # 处理话题词
            if topic_list:
                zhengwen_input.input(" #", append=True)

                # 拼接话题词
                topic_text_for_clipboard = " ".join([
                    topic.strip() if idx == 0 else f"#{topic.strip()}"
                    for idx, topic in enumerate(topic_list)
                ])

                # 设置剪切板内容
                self.device_session.set_clipboard_text(topic_text_for_clipboard)
                logger.info(f"话题词已复制到剪切板: {topic_text_for_clipboard}")
            else:
                logger.info("没有话题词，跳过输入#")

            return True

        except Exception as e:
            logger.error(f"提取和发布内容失败: {e}")
            return False

    def click_photo(self):
        """点击相册按钮"""
        try:
            photo_element = self.device_session.find_by_text("相册")
            if photo_element:
                photo_element.click()
                return True
            else:
                logger.warning("未找到相册按钮")
                return False
        except Exception as e:
            logger.error(f"点击相册按钮失败: {e}")
            return False

    def click_allow(self):
        """点击所有‘允许’按钮"""
        try:
            allow_elements = self.device_session.find_all_by_text("允许")
            if allow_elements:
                allow_elements[0].click()
                return True
            else:
                logger.info("未找到‘允许’按钮")
                return True  # 可能已经有权限了
        except Exception as e:
            logger.warning(f"点击‘允许’按钮失败: {e}")
            return True  # 继续执行


    def click_refresh(self):
        """点击刷新按钮"""
        try:
            refresh_element = self.device_session.find_by_text("刷新")
            if refresh_element:
                refresh_element.click()
                return True
            else:
                logger.info("未找到刷新按钮")
                return True
        except Exception as e:
            logger.warning(f"点击刷新按钮失败: {e}")
            return True

    def verify_release(self):
        """验证发布是否成功"""
        try:
            time.sleep(1)
            page_source = self.device_session.get_page_source()
            if "com.xingin.xhs:id/aql" in page_source or "com.xingin.xhs:id/apg" in page_source:
                return True
            else:
                return False
        except Exception as e:
            logger.error(f"验证发布状态失败: {e}")
            return False

    def handle_scheduled_publish(self, publish_data):
        """处理定时发布"""
        try:
            # 1. 点击高级选项
            logger.info("点击高级选项")
            advanced_option = self.device_session.find_by_id("com.xingin.xhs:id/aof")
            if advanced_option:
                advanced_option.click()
                time.sleep(2)
            else:
                logger.error("未找到高级选项按钮")
                return False

            # 2. 点击定时发布开关
            logger.info("点击定时发布开关")
            scheduled_publish_switch = self.device_session.find_by_xpath('//*[contains(@text, "定时发布")]/following-sibling::*[1]')
            if scheduled_publish_switch:
                scheduled_publish_switch.click()
                time.sleep(2)
            else:
                logger.error("未找到定时发布开关")
                return False

            # 3. 设置发布时间
            release_time = publish_data.get("release_time", "")
            if release_time:
                logger.info(f"设置发布时间: {release_time}")
                if not self.set_publish_time(release_time):
                    logger.error("设置发布时间失败")
                    return False
            else:
                logger.error("未找到发布时间")
                return False
            
            # 定时设置完成返回发布界面
            self.device_session.back()
            
            # 4. 点击定时发布按钮
            logger.info("点击定时发布按钮")
            scheduled_publish_button = self.device_session.find_by_id("com.xingin.xhs:id/alx")
            if scheduled_publish_button:
                scheduled_publish_button.click()
                logger.info("定时发布设置成功")
                return True
            else:
                logger.error("未找到定时发布按钮")
                return False

        except Exception as e:
            logger.error(f"处理定时发布失败: {e}")
            return False

    def set_publish_time(self, release_time):
        """设置发布时间"""
        try:
            # 解析发布时间 "2025-08-08 15:09:00"
            release_datetime = datetime.strptime(release_time, "%Y-%m-%d %H:%M:%S")

            # 获取日期和时间，支持多种格式
            # 去掉前导零的格式，如 "8月8日"
            month = release_datetime.month
            day = release_datetime.day
            target_date = f"{month}月{day}日"

            target_hour = release_datetime.hour
            target_minute = release_datetime.minute

            logger.info(f"目标日期: {target_date}, 目标时间: {target_hour:02d}:{target_minute:02d}")
            logger.info(f"解析的时间: 年={release_datetime.year}, 月={month}, 日={day}, 时={target_hour}, 分={target_minute}")

            # 选择日期
            if not self.select_date(target_date):
                logger.error("选择日期失败")
                return False

            # 选择时间
            if not self.select_time(target_hour, target_minute):
                logger.error("选择时间失败")
                return False

            # 点击完成按钮
            complete_button = self.device_session.find_by_text("完成")
            if complete_button:
                complete_button.click()
                time.sleep(1)
                logger.info("时间设置完成")
                return True
            else:
                logger.error("未找到完成按钮")
                return False

        except Exception as e:
            logger.error(f"设置发布时间失败: {e}")
            return False

    def select_date(self, target_date):
        """选择日期，直接点击对应元素 - 自适应分辨率"""
        try:
            # 获取屏幕尺寸用于位置判断
            window_size = self.device_session.get_window_size()
            screen_width = window_size['width']
            date_threshold = screen_width * 0.6  # 日期选择器在左侧60%以内

            # 先尝试直接点击可见的日期
            all_date_elements = self.device_session.find_all_by_xpath("//android.widget.TextView[contains(@text, '月') and contains(@text, '日')]")

            for element in all_date_elements:
                try:
                    date_text = element.get_attribute("text")
                    bounds = element.get_bounding()

                    if target_date in date_text and bounds != [0, 0, 0, 0]:
                        # 确保是日期选择器区域的元素
                        x_center = (bounds[0] + bounds[2]) / 2
                        if x_center < date_threshold:
                            logger.info(f"找到目标日期: {date_text}")
                            element.click()
                            time.sleep(1)
                            return True
                except:
                    continue

            # 如果没找到可见的，尝试滑动查找
            max_swipes = 20
            for i in range(max_swipes):
                try:
                    elements = self.device_session.find_all_by_xpath("//android.widget.TextView[contains(@text, '月') and contains(@text, '日')]")
                    for el in elements:
                        date_text = el.get_attribute("text")
                        bounds = el.get_bounding()
                        if target_date in date_text and bounds != [0, 0, 0, 0]:
                            x_center = (bounds[0] + bounds[2]) / 2
                            if x_center < date_threshold:
                                el.click()
                                logger.info(f"滑动后找到目标日期: {date_text}")
                                return True
                except:
                    pass

                if i < max_swipes - 1:
                    # 先尝试常规滑动
                    self._swipe_picker_column("date")
                    time.sleep(2.5)  # 增加等待时间，让滑动完全完成

                    # 如果前几次滑动没效果，尝试不同的方法
                    if i > 5 and i % 4 == 0:
                        logger.debug("尝试备用滑动方法 - 日期")
                        self._swipe_picker_alternative("date")
                        time.sleep(3.0)  # 备用方法后等待更久
                    elif i > 10 and i % 5 == 0:
                        logger.debug("尝试简单滑动方法 - 日期")
                        self._swipe_simple("date")
                        time.sleep(3.5)  # 简单方法后等待最久

            logger.error(f"未找到目标日期: {target_date}")
            return False

        except Exception as e:
            logger.error(f"选择日期失败: {e}")
            return False

    def select_time(self, target_hour, target_minute):
        """选择时间，支持滑动查找"""
        try:
            # 选择小时
            if not self._select_time_value(target_hour, "小时"):
                return False

            time.sleep(1)

            # 选择分钟
            if not self._select_time_value(target_minute, "分钟"):
                return False

            return True

        except Exception as e:
            logger.error(f"选择时间失败: {e}")
            return False

    def _select_time_value(self, target_value, value_type):
        """选择时间值（小时或分钟），滑动查找并点击 - 自适应分辨率"""
        try:
            target_text = f"{target_value:02d}"
            max_swipes = 20

            # 获取屏幕尺寸用于自适应位置判断
            window_size = self.device_session.get_window_size()
            screen_width = window_size['width']
            date_threshold = screen_width * 0.6
            hour_threshold = screen_width * 0.85

            for i in range(max_swipes):
                try:
                    elements = self.device_session.find_all_by_xpath(f"//android.widget.TextView[@text='{target_text}']")
                    for el in elements:
                        bounds = el.get_bounding()
                        if bounds != [0, 0, 0, 0]:
                            x_center = (bounds[0] + bounds[2]) / 2

                            # 自适应位置判断
                            if value_type == "小时" and date_threshold <= x_center < hour_threshold:
                                el.click()
                                logger.info(f"选择{value_type}: {target_text}")
                                return True
                            elif value_type == "分钟" and x_center >= hour_threshold:
                                el.click()
                                logger.info(f"选择{value_type}: {target_text}")
                                return True
                except:
                    pass

                if i < max_swipes - 1:
                    # 先尝试常规滑动
                    self._swipe_picker_column(value_type)
                    time.sleep(2.5)  # 增加等待时间，让滑动完全完成

                    # 如果前几次滑动没效果，尝试不同的方法
                    if i > 5 and i % 4 == 0:
                        logger.debug(f"尝试备用滑动方法 - {value_type}")
                        self._swipe_picker_alternative(value_type)
                        time.sleep(3.0)  # 备用方法后等待更久
                    elif i > 10 and i % 5 == 0:
                        logger.debug(f"尝试简单滑动方法 - {value_type}")
                        self._swipe_simple(value_type)
                        time.sleep(3.5)  # 简单方法后等待最久

            logger.error(f"未找到{value_type}: {target_text}")
            return False

        except Exception as e:
            logger.error(f"选择{value_type}失败: {e}")
            return False

    def _swipe_picker_column(self, picker_type):
        """滑动某一列（日期/小时/分钟），多种滑动方式尝试"""
        try:
            # 获取屏幕尺寸
            window_size = self.device_session.get_window_size()
            screen_width = window_size['width']
            screen_height = window_size['height']

            # 根据picker类型确定滑动区域
            if picker_type == "date":
                # 日期选择器区域 (基于UI分析)
                x_center = screen_width * 0.3  # 左侧30%位置
            elif picker_type == "小时":
                # 小时选择器区域
                x_center = screen_width * 0.7  # 中间70%位置
            elif picker_type == "分钟":
                # 分钟选择器区域
                x_center = screen_width * 0.9  # 右侧90%位置
            else:
                return

            # 慢速精确滑动参数
            start_y = screen_height * 0.72  # 从72%高度开始
            end_y = screen_height * 0.62    # 滑动到62%高度 (只滑动10%的距离)
            duration = 2000  # 2秒慢速滑动

            logger.debug(f"滑动{picker_type}: x={x_center:.0f}, {start_y:.0f}→{end_y:.0f}, duration={duration}")

            # 执行滑动
            self.device_session.swipe(x_center, start_y, x_center, end_y, duration=duration)

        except Exception as e:
            logger.debug(f"滑动{picker_type}失败: {e}")

    def _swipe_picker_alternative(self, picker_type):
        """备用滑动方法 - 使用触摸事件序列"""
        try:
            window_size = self.device_session.get_window_size()
            screen_width = window_size['width']
            screen_height = window_size['height']

            # 确定滑动位置
            if picker_type == "date":
                x_center = screen_width * 0.3
            elif picker_type == "小时":
                x_center = screen_width * 0.7
            elif picker_type == "分钟":
                x_center = screen_width * 0.9
            else:
                return

            start_y = screen_height * 0.72  # 从72%高度开始
            end_y = screen_height * 0.62    # 滑动到62%高度 (小距离)

            # 使用ActionChains进行更精确的慢速滑动
            from selenium.webdriver.common.action_chains import ActionChains
            from selenium.webdriver.common.actions import interaction
            from selenium.webdriver.common.actions.action_builder import ActionBuilder
            from selenium.webdriver.common.actions.pointer_input import PointerInput

            actions = ActionChains(self.device_session.driver)
            actions.w3c_actions = ActionBuilder(self.device_session.driver, mouse=PointerInput(interaction.POINTER_TOUCH, "touch"))
            actions.w3c_actions.pointer_action.move_to_location(x_center, start_y)
            actions.w3c_actions.pointer_action.pointer_down()
            actions.w3c_actions.pointer_action.pause(0.5)  # 增加暂停时间
            actions.w3c_actions.pointer_action.move_to_location(x_center, end_y)
            actions.w3c_actions.pointer_action.pause(0.5)  # 增加暂停时间
            actions.w3c_actions.pointer_action.pointer_up()
            actions.perform()

            logger.debug(f"备用滑动{picker_type}: x={x_center:.0f}, {start_y:.0f}→{end_y:.0f}")

        except Exception as e:
            logger.debug(f"备用滑动{picker_type}失败: {e}")

    def _swipe_simple(self, picker_type):
        """最简单的滑动方法 - 直接使用固定坐标"""
        try:
            window_size = self.device_session.get_window_size()
            screen_width = window_size['width']
            screen_height = window_size['height']

            # 基于红米手机UI分析的固定比例
            if picker_type == "date":
                x_center = 317  # 日期选择器中心
            elif picker_type == "小时":
                x_center = 712  # 小时选择器中心
            elif picker_type == "分钟":
                x_center = 936  # 分钟选择器中心
            else:
                return

            # 如果是其他分辨率，按比例调整
            if screen_width != 1080:
                x_center = x_center * (screen_width / 1080)

            start_y = screen_height * 0.72  # 从72%高度开始
            end_y = screen_height * 0.62    # 滑动到62%高度 (小距离慢速)

            logger.debug(f"简单滑动{picker_type}: x={x_center:.0f}, {start_y:.0f}→{end_y:.0f}")
            self.device_session.swipe(x_center, start_y, x_center, end_y, duration=2500)  # 2.5秒超慢速

        except Exception as e:
            logger.debug(f"简单滑动{picker_type}失败: {e}")

    def _swipe_date_picker_down(self):
        """向下滑动日期选择器 - 基于UI树精确定位"""
        try:
            # 方法1: 精确定位日期ListView (bounds="[40,1348][386,1552]")
            try:
                date_listview = self.device_session.find_by_xpath("//android.widget.ListView[@bounds='[40,1348][386,1552]']")
                if date_listview:
                    # 使用ListView的实际边界
                    center_x = (40 + 386) / 2  # 213
                    start_y = 1348 + (1552 - 1348) * 0.8  # 从ListView底部80%开始
                    end_y = 1348 + (1552 - 1348) * 0.2    # 滑动到ListView顶部20%

                    self.device_session.swipe(center_x, start_y, center_x, end_y, duration=400)
                    logger.info(f"精确滑动日期选择器: ({center_x}, {start_y}) -> ({center_x}, {end_y})")
                    return
            except Exception as e:
                logger.debug(f"精确ListView滑动失败: {e}")

            # 方法2: 通用ListView查找
            try:
                date_listviews = self.device_session.find_all_by_xpath("//android.widget.ListView")
                for listview in date_listviews:
                    bounds = listview.get_bounding()
                    # 判断是否是日期选择器 (左侧区域，Y坐标在1300+)
                    if bounds[0] < 100 and bounds[1] > 1300:
                        center_x = (bounds[0] + bounds[2]) / 2
                        start_y = bounds[1] + (bounds[3] - bounds[1]) * 0.8
                        end_y = bounds[1] + (bounds[3] - bounds[1]) * 0.2

                        self.device_session.swipe(center_x, start_y, center_x, end_y, duration=400)
                        logger.info(f"通用ListView滑动日期选择器: ({center_x}, {start_y}) -> ({center_x}, {end_y})")
                        return
            except Exception as e:
                logger.debug(f"通用ListView滑动失败: {e}")

            # 方法3: 固定坐标滑动 (基于UI分析的固定坐标)
            center_x = 213  # (40 + 386) / 2
            start_y = 1511  # 1348 + (1552 - 1348) * 0.8
            end_y = 1389   # 1348 + (1552 - 1348) * 0.2

            self.device_session.swipe(center_x, start_y, center_x, end_y, duration=400)
            logger.info(f"固定坐标滑动日期选择器: ({center_x}, {start_y}) -> ({center_x}, {end_y})")

        except Exception as e:
            logger.error(f"滑动日期选择器失败: {e}")

    def _swipe_time_picker(self, value_type):
        """滑动时间选择器 - 基于UI树精确定位"""
        try:
            if value_type == "小时":
                # 小时选择器 bounds="[416,328][534,1552]"
                try:
                    hour_listview = self.device_session.find_by_xpath("//android.widget.ListView[@bounds='[416,328][534,1552]']")
                    if hour_listview:
                        center_x = (416 + 534) / 2  # 475
                        start_y = 328 + (1552 - 328) * 0.8  # 从ListView底部80%开始
                        end_y = 328 + (1552 - 328) * 0.2    # 滑动到ListView顶部20%

                        self.device_session.swipe(center_x, start_y, center_x, end_y, duration=400)
                        logger.info(f"精确滑动小时选择器: ({center_x}, {start_y}) -> ({center_x}, {end_y})")
                        return
                except Exception as e:
                    logger.debug(f"精确小时ListView滑动失败: {e}")

                # 固定坐标滑动
                center_x = 475  # (416 + 534) / 2
                start_y = 1307  # 328 + (1552 - 328) * 0.8
                end_y = 573    # 328 + (1552 - 328) * 0.2

            else:  # 分钟
                # 分钟选择器 bounds="[564,0][680,1552]"
                try:
                    minute_listview = self.device_session.find_by_xpath("//android.widget.ListView[@bounds='[564,0][680,1552]']")
                    if minute_listview:
                        center_x = (564 + 680) / 2  # 622
                        start_y = 0 + (1552 - 0) * 0.8     # 从ListView底部80%开始
                        end_y = 0 + (1552 - 0) * 0.2       # 滑动到ListView顶部20%

                        self.device_session.swipe(center_x, start_y, center_x, end_y, duration=400)
                        logger.info(f"精确滑动分钟选择器: ({center_x}, {start_y}) -> ({center_x}, {end_y})")
                        return
                except Exception as e:
                    logger.debug(f"精确分钟ListView滑动失败: {e}")

                # 固定坐标滑动
                center_x = 622  # (564 + 680) / 2
                start_y = 1242  # 0 + 1552 * 0.8
                end_y = 310    # 0 + 1552 * 0.2

            self.device_session.swipe(center_x, start_y, center_x, end_y, duration=400)
            logger.info(f"固定坐标滑动{value_type}选择器: ({center_x}, {start_y}) -> ({center_x}, {end_y})")

        except Exception as e:
            logger.error(f"滑动{value_type}选择器失败: {e}")

    def update_mingdao_status(self, item, status="已发布"):
        """更新明道云状态"""
        try:
            url = "https://api.mingdao.com/v2/open/worksheet/editRows"
            headers = {"Content-Type": "application/json"}

            row_id = item.get("rowid")
            if not row_id:
                logger.error("未找到有效的rowid数据")
                return False

            # 构建请求数据
            data = {
                "appKey": "f08bf7f7cfe8c038",
                "sign": "N2RlOTA2MzM2YWNmNjU2ZjE0NGE5ZjhjODhiYWM5MDBiMmIzNTY2NmJkNWYwMWQzMTYwN2U2Y2ZkOTc4YjA1OQ==",
                "worksheetId": "jzzhnrff",
                "rowIds": [row_id],  # 使用 rowid
                "controls": [
                    {
                        "controlId": "release_status",
                        "value": status,
                        "valueType": 1
                    }
                ]
            }

            response = requests.post(url, headers=headers, json=data, timeout=10)
            if response.status_code == 200:
                logger.info(f"明道云状态更新成功: {status}")
                return True
            else:
                logger.error(f"明道云状态更新失败: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"更新明道云状态失败: {e}")
            return False

    def run_publish_flow(self, account_number):
        """运行完整的发布流程"""
        try:
            logger.info(f"🚀 开始执行发布流程，账号: {account_number}")

            # 1. 检查是否有待发布内容
            publish_data = self.check_fabu_config(account_number)
            if not publish_data:
                logger.info("没有待发布内容，跳过发布流程")
                return False

            # 2. 清理设备相册
            self.clean_device_media()

            # 3. 下载媒体文件
            media_type = self.download_media(publish_data)
            if not media_type or media_type == 'unknown':
                logger.error("下载媒体文件失败")
                return False

            logger.info(f"媒体类型: {media_type}")

            # 4. 确保应用状态正常
            self.ensure_app_ready()

            # 5. 点击发布按钮
            if not self.click_publish_button():
                logger.error("点击发布按钮失败")
                return False
            
            time.sleep(1)

            # 新增：红米手机点击“允许访问全部”按钮
            allow_buttons = self.device_session.find_all_by_text("允许访问全部")
            if allow_buttons:
                logger.info("检测到权限弹窗，点击 '允许访问全部'")
                allow_buttons[0].click()
                time.sleep(2)

            # 6. 处理权限和相册
            self.click_allow()
            time.sleep(1)
            # self.click_photo()
            # time.sleep(2)
            # self.click_refresh()
            # time.sleep(3)

            # 7. 选择媒体文件
            if not self.click_fabu_follow(media_type):
                logger.error("选择媒体文件失败")
                return False

            time.sleep(3)

            # 8. 填写发布内容
            if not self.extract_and_publish(publish_data):
                logger.error("填写发布内容失败")
                return False

            time.sleep(2)

            # 9. 检查是否为定时发布
            release_type = publish_data.get("release_type", "")
            if release_type == "定时发布":
                logger.info("检测到定时发布，开始设置定时发布")
                if not self.handle_scheduled_publish(publish_data):
                    logger.error("设置定时发布失败")
                    return False
            else:
                # 10. 立即发布
                publish_final_button = self.device_session.find_by_id("com.xingin.xhs:id/alx")
                if publish_final_button:
                    publish_final_button.click()
                    logger.info("点击最终发布按钮")
                else:
                    logger.error("未找到最终发布按钮")
                    return False

            # 10. 等待发布完成并验证
            if release_type == "定时发布":
                logger.info("✅ 定时发布设置成功！")
                # 更新明道云状态为已定时
                self.update_mingdao_status(publish_data, "已发布")
            else:
                logger.info("✅ 发布成功！")
                # 更新明道云状态为已发布
                self.update_mingdao_status(publish_data, "已发布")

            # 11. 清理设备相册
            self.clean_device_media()
            return True
        
            # time.sleep(10)
            # if self.verify_release():
            #     logger.info("✅ 发布成功！")
            #     # 更新明道云状态
            #     self.update_mingdao_status(publish_data, "已发布")

            #     # 11. 清理设备相册
            #     self.clean_device_media()
            #     return True
            # else:
            #     logger.error("❌ 发布失败")
            #     return False

        except Exception as e:
            logger.error(f"发布流程执行失败: {e}")
            return False
        # finally:
        #     # 确保最后清理相册
        #     try:
        #         self.clean_device_media()
        #     except:
        #         pass
            
    def push_image_to_device(self, local_image_path, device_path="/sdcard/DCIM/Camera/"):
        """将图片推送到设备相册目录"""
        try:
            # 生成唯一的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_extension = os.path.splitext(local_image_path)[1]
            device_filename = f"IMG_{timestamp}{file_extension}"
            full_device_path = f"{device_path}{device_filename}"
            
            # 推送文件到设备
            result = subprocess.run(
                ['adb', '-s', self.custom_udid, 'push', local_image_path, full_device_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                logger.info(f"图片推送成功: {full_device_path}")
                
                # 刷新媒体库，让相册能识别新图片
                self.device_session.execute_script("mobile: shell", {
                    "command": f"am broadcast -a android.intent.action.MEDIA_SCANNER_SCAN_FILE -d file://{full_device_path}"
                })
                
                return full_device_path
            else:
                logger.error(f"图片推送失败: {result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"推送图片到设备失败: {e}")
            return None
            
    def start_publish_flow(self):
        """开始发布流程"""
        try:
            # 确保应用状态正常
            self.ensure_app_ready()
            
            # 点击发布按钮（加号）
            publish_button = self.device_session.find_by_id("com.xingin.xhs:id/du8")
            if publish_button:
                publish_button.click()
                time.sleep(2)
                logger.info("点击发布按钮成功")
                return True
            else:
                logger.error("未找到发布按钮")
                return False
                
        except Exception as e:
            logger.error(f"开始发布流程失败: {e}")
            return False
            
    def select_images_from_album(self, image_count=1):
        """从相册选择图片（通用方法）"""
        try:
            # 等待相册界面加载
            time.sleep(3)
            
            # 查找图片元素（通常是ImageView或类似的容器）
            # 这里使用通用的查找方法，适配不同手机
            image_elements = []
            
            # 方法1：通过常见的图片容器ID查找
            common_image_ids = [
                "com.xingin.xhs:id/image",
                "com.xingin.xhs:id/photo",
                "com.xingin.xhs:id/thumbnail",
                "com.xingin.xhs:id/item_image"
            ]
            
            for image_id in common_image_ids:
                elements = self.device_session.find_all_by_id(image_id)
                if elements:
                    image_elements = elements
                    logger.info(f"通过ID {image_id} 找到 {len(elements)} 个图片")
                    break
            
            # 方法2：如果通过ID找不到，尝试通过class查找
            if not image_elements:
                elements = self.device_session.find_all_by_class_name("android.widget.ImageView")
                # 过滤掉太小的ImageView（可能是图标）
                image_elements = [elem for elem in elements if self._is_valid_image_element(elem)]
                logger.info(f"通过class找到 {len(image_elements)} 个有效图片")
            
            # 选择指定数量的图片
            selected_count = 0
            for i, image_elem in enumerate(image_elements[:image_count]):
                try:
                    image_elem.click()
                    selected_count += 1
                    logger.info(f"选择第 {selected_count} 张图片")
                    time.sleep(1)
                except Exception as e:
                    logger.warning(f"选择第 {i+1} 张图片失败: {e}")
                    
            if selected_count > 0:
                # 点击确认/下一步按钮
                self._click_confirm_button()
                return True
            else:
                logger.error("未能选择任何图片")
                return False
                
        except Exception as e:
            logger.error(f"从相册选择图片失败: {e}")
            return False
            
    def _is_valid_image_element(self, element):
        """判断是否是有效的图片元素"""
        try:
            # 获取元素的bounds
            bounds = element.get_attribute("bounds")
            if bounds:
                # 解析bounds字符串，例如: "[0,0][100,100]"
                import re
                match = re.search(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
                if match:
                    x1, y1, x2, y2 = map(int, match.groups())
                    width = x2 - x1
                    height = y2 - y1
                    # 过滤掉太小的元素（可能是图标）
                    return width > 50 and height > 50
            return True
        except:
            return True
            
    def _click_confirm_button(self):
        """点击确认/下一步按钮"""
        try:
            # 常见的确认按钮文本
            confirm_texts = ["确定", "下一步", "完成", "OK", "Next", "Done"]
            
            for text in confirm_texts:
                elements = self.device_session.find_all_by_xpath(f'//*[@text="{text}"]')
                if elements:
                    elements[0].click()
                    logger.info(f"点击确认按钮: {text}")
                    time.sleep(2)
                    return True
                    
            # 如果找不到文本按钮，尝试通过ID查找
            confirm_ids = [
                "com.xingin.xhs:id/confirm",
                "com.xingin.xhs:id/next",
                "com.xingin.xhs:id/done",
                "android:id/button1"
            ]
            
            for confirm_id in confirm_ids:
                elements = self.device_session.find_all_by_id(confirm_id)
                if elements:
                    elements[0].click()
                    logger.info(f"点击确认按钮ID: {confirm_id}")
                    time.sleep(2)
                    return True
                    
            logger.warning("未找到确认按钮")
            return False
            
        except Exception as e:
            logger.error(f"点击确认按钮失败: {e}")
            return False
            
    def add_title_and_content(self, title, content):
        """添加标题和内容"""
        try:
            # 等待编辑界面加载
            time.sleep(3)
            
            # 查找标题输入框
            title_input = self.device_session.find_by_id("com.xingin.xhs:id/title_edit")
            if title_input:
                title_input.clear()
                title_input.send_keys(title)
                logger.info(f"输入标题: {title}")
            
            # 查找内容输入框
            content_input = self.device_session.find_by_id("com.xingin.xhs:id/content_edit")
            if content_input:
                content_input.clear()
                content_input.send_keys(content)
                logger.info(f"输入内容: {content[:50]}...")
                
            return True
            
        except Exception as e:
            logger.error(f"添加标题和内容失败: {e}")
            return False
            
    def publish_note(self):
        """发布笔记"""
        try:
            # 查找发布按钮
            publish_button = self.device_session.find_by_xpath('//*[@text="发布"]')
            if not publish_button:
                publish_button = self.device_session.find_by_id("com.xingin.xhs:id/publish")
                
            if publish_button:
                publish_button.click()
                logger.info("点击发布按钮")
                time.sleep(5)  # 等待发布完成
                return True
            else:
                logger.error("未找到发布按钮")
                return False
                
        except Exception as e:
            logger.error(f"发布笔记失败: {e}")
            return False
            
    def publish_complete_flow(self, image_paths, title, content):
        """完整的发布流程"""
        try:
            logger.info(f"开始发布笔记: {title}")
            
            # 1. 推送图片到设备
            pushed_images = []
            for image_path in image_paths:
                device_path = self.push_image_to_device(image_path)
                if device_path:
                    pushed_images.append(device_path)
                    
            if not pushed_images:
                logger.error("没有成功推送任何图片")
                return False
                
            # 2. 开始发布流程
            if not self.start_publish_flow():
                return False
                
            # 3. 选择图片
            if not self.select_images_from_album(len(pushed_images)):
                return False
                
            # 4. 添加标题和内容
            if not self.add_title_and_content(title, content):
                return False
                
            # 5. 发布笔记
            if not self.publish_note():
                return False
                
            logger.info("笔记发布成功！")
            return True
            
        except Exception as e:
            logger.error(f"完整发布流程失败: {e}")
            return False
        finally:
            # 清理推送的图片（可选）
            self._cleanup_pushed_images(pushed_images)
            
    def _cleanup_pushed_images(self, image_paths):
        """清理推送的图片"""
        try:
            for image_path in image_paths:
                subprocess.run(
                    ['adb', '-s', self.custom_udid, 'shell', 'rm', image_path],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    timeout=10
                )
            logger.info("清理推送的图片完成")
        except Exception as e:
            logger.warning(f"清理推送的图片失败: {e}")
