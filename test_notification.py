#!/usr/bin/env python3
"""
测试通知API的脚本
"""

import requests
import json
import time

# 配置
BASE_URL = "http://localhost:8080/api"
ANDROID_ID = "0b5c4c84bcf33a9a"

def send_notification(notification_data):
    """发送通知"""
    url = f"{BASE_URL}/xhs/notification"
    
    try:
        response = requests.post(url, json=notification_data)
        print(f"发送通知: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"发送通知失败: {e}")
        return False

def get_notification_status(android_id):
    """获取通知状态"""
    url = f"{BASE_URL}/xhs/notification/status"
    
    try:
        response = requests.get(url, params={"android_id": android_id})
        print(f"获取状态: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.json()
    except Exception as e:
        print(f"获取状态失败: {e}")
        return None

def clear_notifications(android_id):
    """清空通知"""
    url = f"{BASE_URL}/xhs/notification/clear"
    
    try:
        response = requests.post(url, json={"android_id": android_id})
        print(f"清空通知: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"清空通知失败: {e}")
        return False

def main():
    print("=== 小红书通知API测试 ===")
    
    # 1. 测试发送评论通知
    print("\n1. 发送评论通知...")
    comment_notification = {
        "android_id": ANDROID_ID,
        "type": "comment",
        "app_package": "com.xingin.xhs",
        "title": "新评论",
        "text": "有人评论了你的笔记"
    }
    send_notification(comment_notification)
    
    # 2. 测试发送私信通知
    print("\n2. 发送私信通知...")
    private_notification = {
        "android_id": ANDROID_ID,
        "type": "private",
        "app_package": "com.xingin.xhs",
        "title": "新私信",
        "text": "有人给你发私信了"
    }
    send_notification(private_notification)
    
    # 3. 测试发送非小红书通知（应该被忽略）
    print("\n3. 发送非小红书通知...")
    other_notification = {
        "android_id": ANDROID_ID,
        "type": "comment",
        "app_package": "com.other.app",
        "title": "其他应用",
        "text": "其他应用的通知"
    }
    send_notification(other_notification)
    
    # 4. 获取通知状态
    print("\n4. 获取通知状态...")
    status = get_notification_status(ANDROID_ID)
    
    # 5. 清空通知
    print("\n5. 清空通知...")
    clear_notifications(ANDROID_ID)
    
    # 6. 再次获取状态
    print("\n6. 清空后的状态...")
    get_notification_status(ANDROID_ID)

if __name__ == "__main__":
    main()
