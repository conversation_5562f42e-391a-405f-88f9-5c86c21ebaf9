#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定时发布功能测试
"""

import sys
import os
import time
import subprocess
from datetime import datetime


# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.xbot import DeviceSession

class ScheduledPublishTest:
    def __init__(self):
        self.device_session = None
        self.udid = self.get_connected_device()
        if not self.udid:
            raise RuntimeError("未找到可用设备")
        self.setup_device_session()

    def get_connected_device(self):
        """返回首个已连接设备的UDID"""
        try:
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
            lines = result.stdout.strip().split('\n')[1:]  # 跳过第一行
            for line in lines:
                if '\tdevice' in line:
                    return line.split('\t')[0]
        except Exception as e:
            print(f"获取设备失败: {e}")
        return None

    def setup_device_session(self):
        """设置设备会话"""
        self.device_session = DeviceSession(
            udid=self.udid,
            custom_name=f"测试设备_{self.udid}",
            custom_udid=self.udid
        )
        self.device_session.connect()
        print("✅ 成功连接设备")
        
    from datetime import datetime
    import time

    def test_scheduled_publish_flow(self):
        """测试定时发布流程"""
        try:
            print("🚀 开始测试定时发布流程...")

            # 点击高级选项
            print("4. 点击高级选项...")
            try:
                advanced_option = self.device_session.find_by_id("com.xingin.xhs:id/aof")
                advanced_option.click()
                time.sleep(2)
                print("✅ 高级选项点击成功")
            except Exception as e:
                print(f"❌ 高级选项点击失败: {e}")
                return False

            # 点击定时发布开关
            print("5. 点击定时发布开关...")
            try:
                switch = self.device_session.find_by_xpath(
                    '//*[contains(@text, "定时发布")]/following-sibling::*[1]'
                )
                switch.click()
                time.sleep(2)
                print("✅ 定时发布开关点击成功")
            except Exception as e:
                print(f"❌ 定时发布开关点击失败: {e}")
                return False

            # 设置目标时间
            target_time = "2025-08-12 15:59:00"
            if self.test_time_selection(target_time):
                print("✅ 时间选择成功")
                return True
            else:
                print("❌ 时间选择失败")
                return False

        except Exception as e:
            print(f"❌ 测试流程异常: {e}")
            return False


    def test_time_selection(self, release_time):
        """解析时间并滑动选择"""
        try:
            dt = datetime.strptime(release_time, "%Y-%m-%d %H:%M:%S")
            target_date = f"{dt.month}月{dt.day}日"
            target_hour = dt.hour
            target_minute = dt.minute
            print(f"🎯 目标时间: {target_date} {target_hour:02d}:{target_minute:02d}")

            if not self.select_date_with_swipe(target_date):
                return False
            if not self.select_time_value_with_swipe(target_hour, "小时"):
                return False
            if not self.select_time_value_with_swipe(target_minute, "分钟"):
                return False

            # 点击完成
            try:
                done = self.device_session.find_by_text("完成")
                done.click()
                print("✅ 点击完成按钮")
                return True
            except Exception as e:
                print(f"❌ 完成按钮点击失败: {e}")
                return False

        except Exception as e:
            print(f"❌ 时间解析失败: {e}")
            return False


    def select_date_with_swipe(self, target_date):
        """滑动选择目标日期 - 红米12专用"""
        max_swipes = 15

        for i in range(max_swipes):
            try:
                elements = self.device_session.find_all_by_xpath(
                    f"//android.widget.TextView[starts-with(@text, '{target_date}')]"
                )
                for el in elements:
                    bounds = el.get_bounding()
                    if bounds != [0, 0, 0, 0]:
                        x_center = (bounds[0] + bounds[2]) / 2
                        # 确保是日期选择器区域的元素 (X < 600)
                        if x_center < 600:
                            el.click()
                            print(f"✅ 点击日期：{target_date} (x={x_center:.0f})")
                            return True
            except:
                pass

            self._swipe_date_by_week_center()
            print(f"🔁 第 {i+1} 次滑动日期")
            time.sleep(2.8)  # 更长等待时间
        print(f"❌ 未找到目标日期: {target_date}")
        return False


    def select_time_value_with_swipe(self, target_value, value_type):
        """滑动选择小时或分钟 - 红米12专用"""
        target_text = f"{target_value:02d}"
        max_swipes = 15

        # 红米12手机的位置判断阈值
        date_threshold = 600   # 日期选择器 X < 600
        hour_threshold = 850   # 小时选择器 600 <= X < 850
        # 分钟选择器 X >= 850

        for i in range(max_swipes):
            try:
                elements = self.device_session.find_all_by_xpath(
                    f"//android.widget.TextView[@text='{target_text}']"
                )
                for el in elements:
                    bounds = el.get_bounding()
                    if bounds != [0, 0, 0, 0]:
                        x_center = (bounds[0] + bounds[2]) / 2

                        # 红米12专用位置判断
                        if value_type == "小时" and date_threshold <= x_center < hour_threshold:
                            el.click()
                            print(f"✅ 点击小时：{target_text} (x={x_center:.0f})")
                            return True
                        elif value_type == "分钟" and x_center >= hour_threshold:
                            el.click()
                            print(f"✅ 点击分钟：{target_text} (x={x_center:.0f})")
                            return True
            except:
                pass

            # 直接在数字上滑动
            self._swipe_on_number(value_type)

            print(f"🔁 第 {i+1} 次滑动 {value_type}")
            time.sleep(3.5)  # 3.5秒等待
        print(f"❌ 未找到目标{value_type}: {target_text}")
        return False

    def swipe_picker_column(self, picker_type):
        """滑动某一列（日期/小时/分钟），基于 UI 元素 x 坐标定位"""
        try:
            if picker_type == "date":
                xpath = "//android.widget.TextView[contains(@text, '月') and contains(@text, '日')]"
            elif picker_type in ["小时", "分钟"]:
                xpath = "//android.widget.TextView[string-length(@text)=2 and @bounds!='[0,0][0,0]']"
            else:
                print(f"❌ 未知列类型: {picker_type}")
                return

            elements = self.device_session.find_all_by_xpath(xpath)
            x_center = None
            for el in elements:
                bounds = el.get_bounding()
                if bounds == [0, 0, 0, 0]:
                    continue
                x = (bounds[0] + bounds[2]) / 2
                if picker_type == "date" and x < 400:
                    x_center = x
                    break
                if picker_type == "小时" and 400 <= x <= 600:
                    x_center = x
                    break
                if picker_type == "分钟" and x > 600:
                    x_center = x
                    break

            if x_center is None:
                print(f"❌ 未找到滑动位置: {picker_type}")
                return

            # 更小的滑动距离 + 更慢的滑动速度
            start_y = 1300
            end_y = 1200
            self.device_session.swipe(x_center, start_y, x_center, end_y, duration=700)
            print(f"🐢 慢滑 {picker_type}: x={x_center:.0f} ({start_y} → {end_y})")

        except Exception as e:
            print(f"❌ 滑动 {picker_type} 失败: {e}")

    def swipe_picker_column_redmi12(self, picker_type):
        """红米12专用滑动方法 - 多种方式尝试"""
        try:
            # 红米12手机的固定坐标
            if picker_type == "小时":
                x_center = 712  # 小时选择器中心
            elif picker_type == "分钟":
                x_center = 936  # 分钟选择器中心
            else:
                print(f"❌ 未知列类型: {picker_type}")
                return False

            # 尝试多种滑动方式
            swipe_methods = [
                # 方法1: 大幅度滑动
                {"start_y": 2000, "end_y": 1200, "duration": 1500, "name": "大幅度"},
                # 方法2: 超大幅度滑动
                {"start_y": 2200, "end_y": 800, "duration": 2000, "name": "超大幅度"},
                # 方法3: 快速滑动
                {"start_y": 1800, "end_y": 1400, "duration": 500, "name": "快速"},
            ]

            for i, method in enumerate(swipe_methods):
                print(f"� 尝试{method['name']}滑动 {picker_type}: x={x_center}, {method['start_y']} → {method['end_y']}")
                self.device_session.swipe(x_center, method['start_y'], x_center, method['end_y'], duration=method['duration'])

                # 检查是否有效果
                import time
                time.sleep(1)

                # 如果是最后一种方法，尝试ActionChains
                if i == len(swipe_methods) - 1:
                    print(f"🤖 尝试ActionChains滑动 {picker_type}")
                    self._swipe_with_actions(x_center, method['start_y'], method['end_y'])

                return True

        except Exception as e:
            print(f"❌ 红米12滑动 {picker_type} 失败: {e}")
            return False

    def _swipe_with_actions(self, x, start_y, end_y):
        """使用ActionChains进行滑动"""
        try:
            from selenium.webdriver.common.action_chains import ActionChains
            from selenium.webdriver.common.actions import interaction
            from selenium.webdriver.common.actions.action_builder import ActionBuilder
            from selenium.webdriver.common.actions.pointer_input import PointerInput

            actions = ActionChains(self.device_session.driver)
            actions.w3c_actions = ActionBuilder(self.device_session.driver, mouse=PointerInput(interaction.POINTER_TOUCH, "touch"))
            actions.w3c_actions.pointer_action.move_to_location(x, start_y)
            actions.w3c_actions.pointer_action.pointer_down()
            actions.w3c_actions.pointer_action.pause(0.5)
            actions.w3c_actions.pointer_action.move_to_location(x, end_y)
            actions.w3c_actions.pointer_action.pause(0.5)
            actions.w3c_actions.pointer_action.pointer_up()
            actions.perform()
            print(f"✅ ActionChains滑动完成: {x}, {start_y} → {end_y}")

        except Exception as e:
            print(f"❌ ActionChains滑动失败: {e}")

    def swipe_picker_column_redmi12_date(self):
        """红米12专用日期滑动方法 - 多种方式尝试"""
        try:
            x_center = 317  # 日期选择器中心

            # 尝试多种滑动方式
            swipe_methods = [
                # 方法1: 大幅度滑动
                {"start_y": 2200, "end_y": 1200, "duration": 1500, "name": "大幅度"},
                # 方法2: 超大幅度滑动
                {"start_y": 2300, "end_y": 800, "duration": 2000, "name": "超大幅度"},
                # 方法3: 快速滑动
                {"start_y": 2000, "end_y": 1400, "duration": 500, "name": "快速"},
            ]

            for i, method in enumerate(swipe_methods):
                print(f"� 尝试{method['name']}滑动日期: x={x_center}, {method['start_y']} → {method['end_y']}")
                self.device_session.swipe(x_center, method['start_y'], x_center, method['end_y'], duration=method['duration'])

                # 检查是否有效果
                import time
                time.sleep(1)

                # 如果是最后一种方法，尝试ActionChains
                if i == len(swipe_methods) - 1:
                    print(f"🤖 尝试ActionChains滑动日期")
                    self._swipe_with_actions(x_center, method['start_y'], method['end_y'])

                return True

        except Exception as e:
            print(f"❌ 红米12滑动日期失败: {e}")
            return False

    def _swipe_on_listview(self, picker_type):
        """直接在ListView元素上滑动"""
        try:
            # 查找对应的ListView
            if picker_type == "小时":
                # 小时选择器的ListView bounds="[624,489][800,2357]"
                listview_xpath = "//android.widget.ListView[@bounds='[624,489][800,2357]']"
            elif picker_type == "分钟":
                # 分钟选择器的ListView bounds="[849,0][1023,2357]"
                listview_xpath = "//android.widget.ListView[@bounds='[849,0][1023,2357]']"
            else:
                return False

            listview = self.device_session.find_by_xpath(listview_xpath)
            bounds = listview.get_bounding()

            x_center = (bounds[0] + bounds[2]) / 2
            start_y = bounds[1] + (bounds[3] - bounds[1]) * 0.8
            end_y = bounds[1] + (bounds[3] - bounds[1]) * 0.2

            print(f"📱 ListView滑动 {picker_type}: x={x_center:.0f}, {start_y:.0f} → {end_y:.0f}")
            self.device_session.swipe(x_center, start_y, x_center, end_y, duration=1000)
            return True

        except Exception as e:
            print(f"❌ ListView滑动失败: {e}")
            return False

    def _swipe_extreme(self, picker_type):
        """极端滑动方法 - 全屏滑动"""
        try:
            if picker_type == "小时":
                x_center = 712
            elif picker_type == "分钟":
                x_center = 936
            else:
                return False

            # 全屏滑动
            start_y = 2300  # 接近屏幕底部
            end_y = 300     # 接近屏幕顶部

            print(f"💥 极端滑动 {picker_type}: x={x_center}, {start_y} → {end_y}")
            self.device_session.swipe(x_center, start_y, x_center, end_y, duration=3000)
            return True

        except Exception as e:
            print(f"❌ 极端滑动失败: {e}")
            return False

    def _swipe_precise_slow(self, picker_type):
        """适中速度滑动 - 确保能够滑动"""
        try:
            if picker_type == "小时":
                x_center = 712
            elif picker_type == "分钟":
                x_center = 936
            else:
                return False

            # 适中滑动参数 - 稍微快一点，距离稍微大一点
            start_y = 1700  # 选择器下方位置
            end_y = 1500    # 向上滑动200像素 (约2-3个选项高度)
            duration = 1500  # 1.5秒适中速度滑动

            print(f"� 适中滑动 {picker_type}: x={x_center}, {start_y} → {end_y} (200px, 1.5s)")
            self.device_session.swipe(x_center, start_y, x_center, end_y, duration=duration)
            return True

        except Exception as e:
            print(f"❌ 滑动失败: {e}")
            return False

    def _swipe_precise_slow_date(self):
        """适中速度滑动日期 - 确保能够滑动"""
        try:
            x_center = 317  # 日期选择器中心

            # 适中滑动参数 - 稍微快一点，距离稍微大一点
            start_y = 2200  # 日期选择器下方位置
            end_y = 2000    # 向上滑动200像素 (约2-3个选项高度)
            duration = 1500  # 1.5秒适中速度滑动

            print(f"� 适中滑动日期: x={x_center}, {start_y} → {end_y} (200px, 1.5s)")
            self.device_session.swipe(x_center, start_y, x_center, end_y, duration=duration)
            return True

        except Exception as e:
            print(f"❌ 滑动日期失败: {e}")
            return False

    def _swipe_by_column_center(self, picker_type):
        """基于三列中心点的滑动方法"""
        try:
            x_center = self._get_column_center(picker_type)
            if x_center is None:
                print(f"❌ 无法获取{picker_type}列的中心点")
                return False

            # 稍微慢一点的滑动参数
            start_y = 1700  # 选择器下方位置
            end_y = 1500    # 向上滑动200像素 (约2-3个选项高度)
            duration = 2000  # 2秒慢速滑动

            print(f"🔄 滑动 {picker_type}: x={x_center:.0f}, {start_y} → {end_y} (200px, 2s)")
            self.device_session.swipe(x_center, start_y, x_center, end_y, duration=duration)
            return True

        except Exception as e:
            print(f"❌ 滑动失败: {e}")
            return False

    def _get_column_center(self, picker_type):
        """动态获取三列的中心点"""
        try:
            if picker_type == "日期":
                # 查找日期列的元素
                elements = self.device_session.find_all_by_xpath("//android.widget.TextView[contains(@text, '月') and contains(@text, '日')]")
            elif picker_type == "小时":
                # 查找小时列的元素 - 查找两位数字且在中间位置的元素
                elements = self.device_session.find_all_by_xpath("//android.widget.TextView[string-length(@text)=2]")
            elif picker_type == "分钟":
                # 查找分钟列的元素 - 查找两位数字且在右侧位置的元素
                elements = self.device_session.find_all_by_xpath("//android.widget.TextView[string-length(@text)=2]")
            else:
                return None

            # 分析元素位置，找到对应列的中心
            valid_x_positions = []
            for el in elements:
                bounds = el.get_bounding()
                if bounds != [0, 0, 0, 0]:
                    x_center = (bounds[0] + bounds[2]) / 2

                    # 根据位置判断是否属于目标列
                    if picker_type == "日期" and x_center < 600:
                        valid_x_positions.append(x_center)
                    elif picker_type == "小时" and 600 <= x_center < 850:
                        valid_x_positions.append(x_center)
                    elif picker_type == "分钟" and x_center >= 850:
                        valid_x_positions.append(x_center)

            if valid_x_positions:
                # 取平均值作为列的中心点
                column_center = sum(valid_x_positions) / len(valid_x_positions)
                print(f"📍 {picker_type}列中心点: {column_center:.0f} (基于{len(valid_x_positions)}个元素)")
                return column_center
            else:
                print(f"❌ 未找到{picker_type}列的有效元素")
                return None

        except Exception as e:
            print(f"❌ 获取{picker_type}列中心点失败: {e}")
            return None

    def _click_hidden_element(self, target_text, value_type):
        """直接点击隐藏的时间元素"""
        try:
            print(f"🔍 尝试点击隐藏的{value_type}: {target_text}")

            # 查找所有匹配的元素（包括隐藏的）
            elements = self.device_session.find_all_by_xpath(f"//android.widget.TextView[@text='{target_text}']")

            for el in elements:
                bounds = el.get_bounding()

                # 尝试点击隐藏元素
                if bounds == [0, 0, 0, 0]:
                    print(f"🎯 找到隐藏的{value_type}: {target_text}，尝试点击...")
                    try:
                        el.click()
                        print(f"✅ 成功点击隐藏的{value_type}: {target_text}")
                        return True
                    except Exception as e:
                        print(f"❌ 点击隐藏元素失败: {e}")
                        continue

                # 如果是可见元素，检查位置
                else:
                    x_center = (bounds[0] + bounds[2]) / 2
                    date_threshold = 600
                    hour_threshold = 850

                    if value_type == "小时" and date_threshold <= x_center < hour_threshold:
                        el.click()
                        print(f"✅ 点击可见的{value_type}: {target_text}")
                        return True
                    elif value_type == "分钟" and x_center >= hour_threshold:
                        el.click()
                        print(f"✅ 点击可见的{value_type}: {target_text}")
                        return True

            print(f"❌ 未找到{value_type}: {target_text}")
            return False

        except Exception as e:
            print(f"❌ 点击隐藏元素失败: {e}")
            return False

    def _swipe_by_identifier_center(self, picker_type):
        """基于标识中心点滑动"""
        try:
            x_center = None

            if picker_type == "小时":
                # 找第一个小时数字就直接用它的中心点
                hour_elements = self.device_session.find_all_by_xpath("//android.widget.TextView[string-length(@text)=2]")
                for el in hour_elements:
                    bounds = el.get_bounding()
                    if bounds != [0, 0, 0, 0]:
                        x = (bounds[0] + bounds[2]) / 2
                        if 600 <= x < 850:  # 小时区域
                            text = el.get_attribute("text")
                            x_center = x
                            print(f"� 找到小时数字: {text} at x={x:.0f}，直接用这个中心点滑动")
                            break

            elif picker_type == "分钟":
                # 找第一个分钟数字就直接用它的中心点
                minute_elements = self.device_session.find_all_by_xpath("//android.widget.TextView[string-length(@text)=2]")
                for el in minute_elements:
                    bounds = el.get_bounding()
                    if bounds != [0, 0, 0, 0]:
                        x = (bounds[0] + bounds[2]) / 2
                        if x >= 850:  # 分钟区域
                            text = el.get_attribute("text")
                            x_center = x
                            print(f"⏰ 找到分钟数字: {text} at x={x:.0f}，直接用这个中心点滑动")
                            break

            if x_center is None:
                print(f"❌ 无法找到{picker_type}的标识中心点")
                return False

            # 更慢的滑动速度
            start_y = 1650
            end_y = 1450
            duration = 2500  # 2.5秒，更慢

            print(f"🔄 基于标识滑动 {picker_type}: x={x_center:.0f}, {start_y} → {end_y} (200px, 1.8s)")
            self.device_session.swipe(x_center, start_y, x_center, end_y, duration=duration)
            return True

        except Exception as e:
            print(f"❌ 标识滑动失败: {e}")
            return False

    def _swipe_date_by_week_center(self):
        """基于"周"字中心点滑动日期"""
        try:
            # 查找包含"周"字的元素，找到第一个就用它的中心点
            week_elements = self.device_session.find_all_by_xpath("//android.widget.TextView[contains(@text, '周')]")

            x_center = None
            for el in week_elements:
                bounds = el.get_bounding()
                if bounds != [0, 0, 0, 0]:
                    x = (bounds[0] + bounds[2]) / 2
                    if x < 600:  # 日期区域
                        text = el.get_attribute('text')
                        x_center = x
                        print(f"📅 找到周标识: {text} at x={x:.0f}，直接用这个中心点滑动")
                        break

            if x_center is None:
                print("❌ 未找到周标识")
                return False

            # 更慢的滑动速度
            start_y = 2150
            end_y = 1950
            duration = 2500  # 2.5秒，更慢

            print(f"🔄 基于周字滑动日期: x={x_center:.0f}, {start_y} → {end_y} (200px, 1.8s)")
            self.device_session.swipe(x_center, start_y, x_center, end_y, duration=duration)
            return True

        except Exception as e:
            print(f"❌ 周字滑动失败: {e}")
            return False

    def _get_column_x_coordinate(self, picker_type):
        """获取列的X坐标，只需要获取一次"""
        try:
            if picker_type == "小时":
                # 找第一个小时数字，获取X坐标
                hour_elements = self.device_session.find_all_by_xpath("//android.widget.TextView[string-length(@text)=2]")
                for el in hour_elements:
                    bounds = el.get_bounding()
                    if bounds != [0, 0, 0, 0]:
                        x = (bounds[0] + bounds[2]) / 2
                        if 600 <= x < 850:  # 小时区域
                            text = el.get_attribute("text")
                            print(f"找到小时数字: {text}，获取X坐标: {x:.0f}")
                            return x

            elif picker_type == "分钟":
                # 找第一个分钟数字，获取X坐标
                minute_elements = self.device_session.find_all_by_xpath("//android.widget.TextView[string-length(@text)=2]")
                for el in minute_elements:
                    bounds = el.get_bounding()
                    if bounds != [0, 0, 0, 0]:
                        x = (bounds[0] + bounds[2]) / 2
                        if x >= 850:  # 分钟区域
                            text = el.get_attribute("text")
                            print(f"找到分钟数字: {text}，获取X坐标: {x:.0f}")
                            return x

            print(f"❌ 无法找到{picker_type}的数字元素")
            return None

        except Exception as e:
            print(f"❌ 获取{picker_type}坐标失败: {e}")
            return None
        
    def _swipe_on_number(self, picker_type):
        """最优滑动方案：基于UI树精确坐标，小幅度慢速滑动"""
        try:
            # 基于UI树分析的精确坐标
            if picker_type == "小时":
                # 小时ListView: [624,489][800,2357]
                # 小时数字X坐标: 682-745，中心=714
                x_center = 714
                start_y = 1400  # ListView中间区域
                end_y = 1320    # 小幅度向上滑动80px

            elif picker_type == "分钟":
                # 分钟ListView: [849,0][1023,2357]
                # 分钟数字X坐标: 904-968，中心=936
                x_center = 936
                start_y = 1300  # ListView中间区域
                end_y = 1220    # 小幅度向上滑动80px

            else:
                return False

            # 慢速滑动参数
            duration = 3000  # 3秒慢速

            print(f"🔄 {picker_type}滑动: x={x_center}, {start_y}→{end_y} (80px, 3s)")
            self.device_session.swipe(x_center, start_y, x_center, end_y, duration=duration)
            return True

        except Exception as e:
            print(f"❌ {picker_type}滑动失败: {e}")
            return False


    def cleanup(self):
        """清理资源"""
        if self.device_session:
            self.device_session.quit()
            print("✅ 设备会话已清理")

def main():
    """主函数"""
    tester = None
    try:
        print("🔧 初始化定时发布测试...")
        tester = ScheduledPublishTest()

        # 执行定时发布流程测试
        success = tester.test_scheduled_publish_flow()

        if success:
            print("🎉 定时发布功能测试成功！")
        else:
            print("💥 定时发布功能测试失败！")

    except Exception as e:
        print(f"❌ 测试初始化失败: {e}")
    finally:
        if tester:
            tester.cleanup()

if __name__ == "__main__":
    main()
